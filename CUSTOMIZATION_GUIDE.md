# دليل التخصيص السريع

## تغيير إعدادات الموقع

### تحرير ملف `settings.json`:
```json
{
    "site_name": "اسم موقعك هنا",
    "site_logo": "img/your-logo.svg",
    "site_favicon": "img/your-favicon.ico",
    "site_description": "وصف موقعك",
    "site_keywords": "كلمات مفتاحية, ألعاب, عملات",
    "site_footer": "© 2025 اسم موقعك - جميع الحقوق محفوظة",
    "theme_color": "#4a00e0",
    "tagline": "شعارك النصي هنا",
    "main_title": "العنوان الرئيسي لموقعك",
    "search_title": "ابحث عن تطبيقك",
    "search_placeholder": "ابحث عن التطبيقات..."
}
```

## إضافة لعبة جديدة

### تحرير ملف `games.json`:
```json
{
    "games": [
        {
            "id": "معرف_فريد_للعبة",
            "name": "اسم اللعبة",
            "currency": "اسم العملة",
            "backgroundColor": "#لون_بصيغة_hex",
            "available": true,
            "image": "مسار/صورة/اللعبة.jpg",
            "currencyIcon": "مسار/أيقونة/العملة.png",
            "currencyAmounts": [500, 1000, 1500, 2000, 2500],
            "cpa_script": "",
            "cpa_function": "",
            "last_update": "1 hour ago"
        }
    ]
}
```

## تخصيص كميات العملة

### مثال لكميات مختلفة:
```json
"currencyAmounts": [100, 500, 1000, 2000, 5000, 10000]
```

## تغيير الألوان والتصميم

### تحرير ملف `styles.css` أو `game.css`:
```css
/* تغيير اللون الأساسي */
:root {
    --primary-color: #your-color;
    --secondary-color: #your-secondary-color;
}
```

## إضافة صور جديدة

1. ضع الصور في مجلد `img/`
2. حدث المسارات في `games.json`
3. تأكد من أن أسماء الملفات صحيحة

## نصائح مهمة

### ✅ افعل:
- استخدم صور بجودة عالية
- تأكد من صحة صيغة JSON
- اختبر التغييرات في المتصفح
- احتفظ بنسخة احتياطية

### ❌ لا تفعل:
- لا تحذف الملفات الأساسية
- لا تغير هيكل JSON
- لا تستخدم مسارات خاطئة للصور

## اختبار التغييرات

1. احفظ الملفات المحررة
2. حدث الصفحة في المتصفح (F5)
3. تحقق من عدم وجود أخطاء في Console (F12)

## استكشاف الأخطاء

### إذا لم تظهر الألعاب:
- تحقق من صحة ملف `games.json`
- تأكد من وجود الصور في المسارات المحددة

### إذا لم تعمل الإعدادات:
- تحقق من صحة ملف `settings.json`
- تأكد من عدم وجود فواصل إضافية

### أدوات مفيدة:
- JSON Validator لفحص صحة ملفات JSON
- Developer Tools في المتصفح (F12)
- Console للتحقق من الأخطاء
