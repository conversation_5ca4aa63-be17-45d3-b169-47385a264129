# تحديث كميات العملة المخصصة

## الميزات الجديدة

تم إضافة إمكانية تخصيص كميات العملة لكل لعبة بدلاً من استخدام القيم الثابتة.

### التغييرات المطبقة:

#### 1. ملف games.json
- تم إضافة حقل `currencyAmounts` لكل لعبة
- يحتوي على مصفوفة من الأرقام التي تمثل كميات العملة المتاحة
- مثال: `"currencyAmounts": [600, 1200, 1600, 2400, 3000]`

#### 2. لوحة الإدارة (Admin/games.php)
- تم إضافة حقل "Currency Amounts" في نماذج إضافة وتحديث الألعاب
- يمكن إدخال الكميات مفصولة بفواصل (مثل: 600, 1200, 1600)
- يتم التحقق من صحة البيانات وتحويلها إلى أرقام صحيحة
- إذا لم يتم تحديد كميات، يتم استخدام القيم الافتراضية [600, 1200, 1600]

#### 3. صفحة اللعبة (game.php)
- تم تحديث الكود لاستخدام كميات العملة من بيانات اللعبة
- يتم إنشاء خيارات العملة ديناميكياً بناءً على البيانات المحفوظة

#### 4. JavaScript (game.js)
- تم إضافة دالة `updateCurrencyOptions()` لتحديث خيارات العملة ديناميكياً
- تم تحديث دالة `setupCurrencySelection()` لاستخدام event delegation
- يدعم الآن أي عدد من خيارات العملة

#### 5. التصميم (game.css)
- تم تحسين تصميم خيارات العملة لدعم عدد أكبر من الخيارات
- إضافة flex-wrap للتعامل مع الخيارات الإضافية
- تحسينات للأجهزة المحمولة

## كيفية الاستخدام:

### إضافة لعبة جديدة:
1. اذهب إلى لوحة الإدارة
2. اختر "Add New Game"
3. املأ البيانات المطلوبة
4. في حقل "Currency Amounts" أدخل الكميات مفصولة بفواصل
   مثال: `500, 1000, 1500, 2000, 2500`
5. احفظ اللعبة

### تحديث لعبة موجودة:
1. اذهب إلى لوحة الإدارة
2. اختر "Edit" للعبة المطلوبة
3. عدّل حقل "Currency Amounts"
4. احفظ التغييرات

## الملفات المحدثة:
- `games.json` - إضافة كميات العملة للألعاب الموجودة
- `Admin/games.php` - إضافة إدارة كميات العملة
- `game.php` - استخدام كميات العملة الديناميكية
- `game.js` - دعم خيارات العملة الديناميكية
- `game.css` - تحسينات التصميم

## ملاحظات:
- إذا لم يتم تحديد كميات عملة، سيتم استخدام القيم الافتراضية [600, 1200, 1600]
- يمكن إضافة أي عدد من كميات العملة
- يتم التحقق من صحة البيانات تلقائياً
- التصميم متجاوب ويدعم الأجهزة المحمولة

## اختبار الميزة:
1. افتح `http://localhost:8000/test_currency_amounts.php` لعرض كميات العملة
2. افتح `http://localhost:8000/game.php?id=Roblox` لاختبار اللعبة
3. اختبر عملية اختيار كميات العملة المختلفة
