<?php
// تهيئة متغيرات PHP
$currentTime = date("Y-m-d H:i:s");
$userIP = $_SERVER['REMOTE_ADDR'];

// تضمين ملف الترويسة
include 'header.php';
?>
<div class="container">
    <div class="left-section">
        <div class="logo-section">
            <div class="rocket-logo">
                <img src="<?php echo $siteLogo; ?>" alt="<?php echo $siteName; ?> Logo" id="rocket-img">
            </div>
            <h2 class="site-name"><?php echo $siteName; ?></h2>
            <p class="tagline">Generating Currencies for Android and iOS.</p>
        </div>

        <div class="search-section">
            <h3 class="search-title">FIND YOUR APP</h3>
            <div class="search-container">
                <input type="text" id="search-input" placeholder="Search for apps...">
            </div>
        </div>
    </div>

    <div class="right-section">
        <h1 class="main-title">The First Site in Generating Currencies</h1>

        <div class="game-cards" id="game-cards-container">
            <!-- Game cards will be dynamically added here -->
            <?php
            // قراءة بيانات الألعاب من ملف JSON
            $gamesFile = $settings['games_file'] ?? 'games.json';
            if (file_exists($gamesFile)) {
                $gamesData = json_decode(file_get_contents($gamesFile), true);
                if (isset($gamesData['games']) && is_array($gamesData['games'])) {
                    foreach ($gamesData['games'] as $game) {
                        // عرض الألعاب المتاحة فقط
                        if (isset($game['available']) && $game['available'] === true) {
                            echo '<div class="game-card" data-id="' . $game['id'] . '">';
                            echo '<div class="game-image" style="background-color: ' . ($game['backgroundColor'] ?? '#1e88e5') . ';">';
                            // تحديد ما إذا كان مسار الصورة مطلقًا أو نسبيًا
                            $imageSrc = $game['image'] ?? 'img/placeholder-game.svg';

                            // التحقق مما إذا كان المسار يحتوي على علامة EXTERNAL_URL:
                            if (strpos($imageSrc, 'EXTERNAL_URL:') === 0) {
                                // استخراج الرابط الفعلي
                                $imageSrc = substr($imageSrc, strlen('EXTERNAL_URL:'));
                                $isExternalImage = true;
                            } else {
                                $isExternalImage = (strpos($imageSrc, 'http://') === 0 || strpos($imageSrc, 'https://') === 0);
                            }

                            // إضافة سمة crossorigin للصور الخارجية
                            $crossOrigin = $isExternalImage ? ' crossorigin="anonymous"' : '';

                            echo '<img src="' . $imageSrc . '" alt="' . $game['name'] . '"' . $crossOrigin . '>';
                            echo '</div>';
                            echo '<div class="game-info">';
                            echo '<h3 class="game-title">' . $game['name'] . '</h3>';
                            echo '<p class="game-description">Generate ' . $game['currency'] . ' for ' . $game['name'] . '</p>';
                            echo '<a href="game.php?id=' . $game['id'] . '" class="game-button">Generate</a>';
                            echo '</div>';
                            echo '</div>';
                        }
                    }
                }
            }
            ?>
        </div>
    </div>
</div>

<?php
// إضافة معلومات تصحيح في وضع التطوير
if (isset($_GET['debug']) && $_GET['debug'] === 'true') {
    echo '<div style="position: fixed; bottom: 0; left: 0; background: rgba(0,0,0,0.7); color: white; padding: 10px; font-size: 12px;">';
    echo 'Server Time: ' . $currentTime . '<br>';
    echo 'Your IP: ' . $userIP . '<br>';
    echo 'PHP Version: ' . phpversion();
    echo '</div>';
}

// تضمين ملف التذييل
include 'footer.php';
?>
