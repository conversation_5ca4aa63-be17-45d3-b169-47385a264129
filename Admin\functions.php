<?php
// منع الوصول المباشر للملف
if (!defined('ADMIN_ACCESS')) {
    header('Location: ../index.php');
    exit;
}

// دالة لقراءة بيانات الألعاب من ملف JSON
function getGames() {
    $settings = getSiteSettings();
    $gamesFile = $settings['games_file'];

    if (file_exists($gamesFile)) {
        $gamesData = json_decode(file_get_contents($gamesFile), true);
        return $gamesData['games'] ?? [];
    }

    return [];
}

// دالة لحفظ بيانات الألعاب في ملف JSON
function saveGames($games) {
    $settings = getSiteSettings();
    $gamesFile = $settings['games_file'];

    $gamesData = ['games' => $games];
    return file_put_contents($gamesFile, json_encode($gamesData, JSON_PRETTY_PRINT));
}

// دالة للحصول على لعبة بواسطة المعرف
function getGameById($gameId) {
    $games = getGames();

    foreach ($games as $game) {
        if ($game['id'] === $gameId) {
            return $game;
        }
    }

    return null;
}

// دالة لإضافة لعبة جديدة
function addGame($gameData) {
    $games = getGames();

    // التحقق من وجود اللعبة
    foreach ($games as $game) {
        if ($game['id'] === $gameData['id']) {
            return false; // اللعبة موجودة بالفعل
        }
    }

    // إضافة اللعبة الجديدة
    $games[] = $gameData;

    // حفظ البيانات
    return saveGames($games);
}

// دالة لتحديث لعبة موجودة
function updateGame($gameId, $gameData) {
    $games = getGames();
    $updated = false;

    // البحث عن اللعبة وتحديثها
    foreach ($games as $key => $game) {
        if ($game['id'] === $gameId) {
            $games[$key] = $gameData;
            $updated = true;
            break;
        }
    }

    if ($updated) {
        // حفظ البيانات
        return saveGames($games);
    }

    return false;
}

// دالة لحذف لعبة
function deleteGame($gameId) {
    $games = getGames();
    $deleted = false;

    // البحث عن اللعبة وحذفها
    foreach ($games as $key => $game) {
        if ($game['id'] === $gameId) {
            unset($games[$key]);
            $deleted = true;
            break;
        }
    }

    if ($deleted) {
        // إعادة ترتيب المصفوفة
        $games = array_values($games);

        // حفظ البيانات
        return saveGames($games);
    }

    return false;
}

// دالة للتحقق من صحة رابط الصورة
function validateImageUrl($url) {
    // التحقق من صحة الرابط
    if (filter_var($url, FILTER_VALIDATE_URL) === false) {
        return [
            'success' => false,
            'message' => 'Invalid URL format'
        ];
    }

    // تحديد ما إذا كان الرابط يشير إلى صورة بناءً على امتداد الملف
    $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'];
    $urlPath = parse_url($url, PHP_URL_PATH);
    $extension = strtolower(pathinfo($urlPath, PATHINFO_EXTENSION));

    $isImageByExtension = in_array($extension, $imageExtensions);

    // إذا كان الامتداد يشير إلى صورة، نتخطى التحقق من الرأس
    if (!$isImageByExtension) {
        // التحقق من أن الرابط يشير إلى صورة عن طريق الرأس
        $headers = @get_headers($url, 1);
        if ($headers === false) {
            return [
                'success' => false,
                'message' => 'Could not access URL'
            ];
        }

        // التحقق من نوع المحتوى
        if (isset($headers['Content-Type'])) {
            $contentType = is_array($headers['Content-Type']) ? $headers['Content-Type'][0] : $headers['Content-Type'];
            if (strpos($contentType, 'image/') === false) {
                return [
                    'success' => false,
                    'message' => 'URL does not point to an image'
                ];
            }
        } else {
            // إذا لم نتمكن من التحقق من نوع المحتوى، نفترض أنه صورة إذا كان الامتداد يشير إلى صورة
            if (!$isImageByExtension) {
                return [
                    'success' => false,
                    'message' => 'Could not verify image type'
                ];
            }
        }
    }

    // تحديد ما إذا كان الرابط مطلقًا أو نسبيًا
    $isAbsoluteUrl = (strpos($url, 'http://') === 0 || strpos($url, 'https://') === 0);

    // تأكد من أن الرابط المطلق يتم حفظه كما هو بدون تغيير
    $relativePath = $url;

    // إضافة علامة خاصة للروابط المطلقة لمنع تحويلها إلى مسارات نسبية
    if ($isAbsoluteUrl) {
        $relativePath = 'EXTERNAL_URL:' . $url;
    }

    return [
        'success' => true,
        'file_path' => $url,
        'relative_path' => $relativePath,
        'is_external' => $isAbsoluteUrl
    ];
}

// دالة لتحميل ملف
function uploadFile($file, $targetDir, $fileType = 'general', $allowedTypes = ['image/jpeg', 'image/png', 'image/svg+xml']) {
    // التحقق من وجود خطأ في التحميل
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return [
            'success' => false,
            'message' => 'Error uploading file: ' . $file['error']
        ];
    }

    // التحقق من نوع الملف
    if (!in_array($file['type'], $allowedTypes)) {
        return [
            'success' => false,
            'message' => 'Invalid file type. Allowed types: ' . implode(', ', $allowedTypes)
        ];
    }

    // تحديد المجلد المناسب بناءً على نوع الملف
    $uploadsBaseDir = __DIR__ . '/Uploads/';

    switch ($fileType) {
        case 'favicon':
            $uploadDir = $uploadsBaseDir . 'Favicon/';
            break;
        case 'logo':
            $uploadDir = $uploadsBaseDir . 'SiteLogo/';
            break;
        case 'game':
            $uploadDir = $uploadsBaseDir . 'GamesImg/';
            break;
        case 'currency':
            $uploadDir = $uploadsBaseDir . 'CurrencyIcons/';
            break;
        default:
            $uploadDir = $targetDir; // استخدام المجلد المحدد إذا لم يتم تحديد نوع
    }

    // إنشاء اسم فريد للملف
    $fileName = time() . '_' . basename($file['name']);
    $targetFilePath = $uploadDir . $fileName;

    // التحقق من وجود المجلد وإنشائه إذا لم يكن موجودًا
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // تحميل الملف
    if (move_uploaded_file($file['tmp_name'], $targetFilePath)) {
        // تحديد المسار النسبي للملف للاستخدام في HTML
        // استخدام مسار نسبي بدلاً من مسار مطلق
        $relativePath = '';

        switch ($fileType) {
            case 'favicon':
                $relativePath = '../Admin/Uploads/Favicon/' . $fileName;
                break;
            case 'logo':
                $relativePath = '../Admin/Uploads/SiteLogo/' . $fileName;
                break;
            case 'game':
                $relativePath = '../Admin/Uploads/GamesImg/' . $fileName;
                break;
            case 'currency':
                $relativePath = '../Admin/Uploads/CurrencyIcons/' . $fileName;
                break;
            default:
                // تحويل المسار المطلق إلى مسار نسبي
                $relativePath = str_replace($_SERVER['DOCUMENT_ROOT'], '', $targetFilePath);
                $relativePath = str_replace('\\', '/', $relativePath);
                if (substr($relativePath, 0, 1) !== '/') {
                    $relativePath = '/' . $relativePath;
                }
        }

        return [
            'success' => true,
            'file_path' => $targetFilePath,
            'relative_path' => $relativePath,
            'file_name' => $fileName
        ];
    } else {
        return [
            'success' => false,
            'message' => 'Failed to upload file'
        ];
    }
}

// دالة لعرض رسالة تنبيه
function showAlert($message, $type = 'success') {
    return '<div class="alert alert-' . $type . '">' . $message . '</div>';
}

// دالة للتحقق من وجود رسالة تنبيه
function hasAlert() {
    return isset($_SESSION['alert_message']) && isset($_SESSION['alert_type']);
}

// دالة لتعيين رسالة تنبيه
function setAlert($message, $type = 'success') {
    $_SESSION['alert_message'] = $message;
    $_SESSION['alert_type'] = $type;
}

// دالة لعرض رسالة التنبيه وحذفها
function displayAlert() {
    if (hasAlert()) {
        $alert = showAlert($_SESSION['alert_message'], $_SESSION['alert_type']);
        unset($_SESSION['alert_message']);
        unset($_SESSION['alert_type']);
        return $alert;
    }

    return '';
}
?>
