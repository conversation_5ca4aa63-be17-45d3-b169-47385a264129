# إعادة توجيه الطلبات إلى ملفات PHP
<IfModule mod_rewrite.c>
    RewriteEngine On

    # إعادة توجيه الطلبات إلى مجلد Admin بغض النظر عن حالة الأحرف (مث<PERSON>, admin, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
    RewriteRule ^[Aa][Dd][Mm][Ii][Nn]/?$ Admin/index.php [L]

    # إعادة توجيه الطلبات إلى ملفات داخل مجلد Admin بغض النظر عن حالة الأحرف
    RewriteRule ^[Aa][Dd][Mm][Ii][Nn]/(.*)$ Admin/$1 [L]

    # إعادة توجيه الطلبات إلى ملفات PHP
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME}.php -f
    RewriteRule ^(.*)$ $1.php [L]

    # إعادة توجيه الصفحة الرئيسية
    DirectoryIndex index.php
</IfModule>

# منع عرض محتويات المجلد
Options -Indexes
