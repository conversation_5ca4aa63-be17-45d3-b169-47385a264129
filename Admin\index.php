<?php
// تعريف ثابت للتحقق من الوصول
define('ADMIN_ACCESS', true);

// بدء جلسة
session_start();

// تضمين ملفات الإعدادات والوظائف
require_once 'config.php';
require_once 'functions.php';

// التحقق من تسجيل الخروج
if (isset($_GET['logout']) && $_GET['logout'] == 1) {
    logout();
    header('Location: login.php');
    exit;
}

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// عنوان الصفحة
$pageTitle = 'Dashboard';

// الحصول على إحصائيات
$games = getGames();
$totalGames = count($games);
$activeGames = 0;

foreach ($games as $game) {
    if (isset($game['available']) && $game['available'] === true) {
        $activeGames++;
    }
}

// تضمين ملف الرأس
include 'header.php';
?>

<div class="row">
    <div class="col-lg-8">
        <!-- Stats Cards -->
        <div class="dashboard-stats">
            <div class="stat-card">
                <div class="stat-icon games">
                    <i class="fas fa-gamepad"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo $totalGames; ?></h3>
                    <p>Total Games</p>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon users">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo $activeGames; ?></h3>
                    <p>Active Games</p>
                    <div class="progress mt-2" style="height: 5px;">
                        <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $totalGames > 0 ? ($activeGames / $totalGames * 100) : 0; ?>%" aria-valuenow="<?php echo $activeGames; ?>" aria-valuemin="0" aria-valuemax="<?php echo $totalGames; ?>"></div>
                    </div>
                </div>
            </div>


        </div>

        <!-- Recent Games -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Recent Games</h5>
                <a href="games.php" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                <?php if (empty($games)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No games found. <a href="games.php?action=add" class="alert-link">Add a new game</a>.
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="games-table table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Image</th>
                                <th>Name</th>
                                <th>Currency</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            // عرض آخر 5 ألعاب
                            $recentGames = array_slice($games, 0, 5);
                            foreach ($recentGames as $game):
                            ?>
                            <tr>
                                <td>
                                    <img src="<?php echo $game['image'] ?? '../img/placeholder-game.svg'; ?>" alt="<?php echo $game['name']; ?>" class="game-image">
                                </td>
                                <td>
                                    <strong><?php echo $game['name']; ?></strong>
                                    <?php if (isset($game['last_update'])): ?>
                                    <div class="small text-muted">Updated: <?php echo $game['last_update']; ?></div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (isset($game['currencyIcon']) && !empty($game['currencyIcon'])): ?>
                                    <div class="d-flex align-items-center">
                                        <img src="<?php echo $game['currencyIcon']; ?>" alt="Currency" style="width: 20px; height: 20px; margin-right: 5px;">
                                        <?php echo $game['currency'] ?? 'N/A'; ?>
                                    </div>
                                    <?php else: ?>
                                    <?php echo $game['currency'] ?? 'N/A'; ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (isset($game['available']) && $game['available'] === true): ?>
                                    <span class="badge bg-success">Active</span>
                                    <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                    <?php endif; ?>
                                </td>
                                <td class="game-actions">
                                    <div class="btn-group">
                                        <a href="../game.php?id=<?php echo $game['id']; ?>" class="btn btn-sm btn-info" target="_blank" title="View Game">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="games.php?action=edit&id=<?php echo $game['id']; ?>" class="btn btn-sm btn-warning" title="Edit Game">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="games.php?action=delete&id=<?php echo $game['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this game?');" title="Delete Game">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="games.php?action=add" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i> Add New Game
                    </a>
                    <a href="settings.php" class="btn btn-info text-white">
                        <i class="fas fa-cog me-2"></i> Site Settings
                    </a>
                    <a href="../index.php" class="btn btn-success" target="_blank">
                        <i class="fas fa-external-link-alt me-2"></i> View Website
                    </a>
                </div>
            </div>
        </div>

        <!-- System Info -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>System Info</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-server me-2"></i>PHP Version</span>
                        <span class="badge bg-primary"><?php echo phpversion(); ?></span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-hdd me-2"></i>Free Disk Space</span>
                        <span class="badge bg-info"><?php echo round(disk_free_space("/") / (1024 * 1024 * 1024), 2); ?> GB</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-calendar-alt me-2"></i>Server Time</span>
                        <span class="badge bg-secondary"><?php echo date('Y-m-d H:i:s'); ?></span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-file me-2"></i>Settings File</span>
                        <span class="badge bg-<?php echo is_writable('settings.json') ? 'success' : 'danger'; ?>">
                            <?php echo is_writable('settings.json') ? 'Writable' : 'Not Writable'; ?>
                        </span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-file-alt me-2"></i>Games File</span>
                        <span class="badge bg-<?php echo is_writable('../games.json') ? 'success' : 'danger'; ?>">
                            <?php echo is_writable('../games.json') ? 'Writable' : 'Not Writable'; ?>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php
// تضمين ملف التذييل
include 'footer.php';
?>
