<?php
// تعريف ثابت للتحقق من الوصول
define('ADMIN_ACCESS', true);

// بدء جلسة
session_start();

// تضمين ملفات الإعدادات والوظائف
require_once 'config.php';
require_once 'functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// عنوان الصفحة
$pageTitle = 'Site Settings';

// الحصول على إعدادات الموقع
$settings = getSiteSettings();

// معالجة نموذج تحديث الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $newSettings = [
        'site_name' => $_POST['site_name'] ?? 'Mppl.pro',
        'admin_email' => $_POST['admin_email'] ?? '',
        'site_logo' => $settings['site_logo'],
        'site_favicon' => $settings['site_favicon'],
        'admin_username' => $settings['admin_username'] // حفظ اسم المستخدم الحالي كقيمة افتراضية
    ];

    // معالجة تغيير اسم المستخدم
    $newUsername = $_POST['admin_username'] ?? '';
    if (!empty($newUsername) && $newUsername !== $settings['admin_username']) {
        $newSettings['admin_username'] = $newUsername;
        setAlert('Username updated successfully', 'success');
    }

    // معالجة تغيير كلمة المرور
    $currentPassword = $_POST['current_password'] ?? '';
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';

    if (!empty($currentPassword) && !empty($newPassword)) {
        if ($newPassword !== $confirmPassword) {
            setAlert('New password and confirmation do not match', 'danger');
        } else if (!password_verify($currentPassword, $settings['admin_password'])) {
            setAlert('Current password is incorrect', 'danger');
        } else {
            $newSettings['admin_password'] = password_hash($newPassword, PASSWORD_BCRYPT);
            setAlert('Password updated successfully', 'success');
        }
    }

    // معالجة تحميل الشعار
    if (isset($_FILES['site_logo']) && $_FILES['site_logo']['error'] === UPLOAD_ERR_OK) {
        $uploadResult = uploadFile($_FILES['site_logo'], '', 'logo');
        if ($uploadResult['success']) {
            $newSettings['site_logo'] = $uploadResult['relative_path'];
        } else {
            setAlert('Error uploading logo: ' . $uploadResult['message'], 'danger');
        }
    }

    // معالجة تحميل أيقونة الموقع
    if (isset($_FILES['site_favicon']) && $_FILES['site_favicon']['error'] === UPLOAD_ERR_OK) {
        $uploadResult = uploadFile($_FILES['site_favicon'], '', 'favicon');
        if ($uploadResult['success']) {
            $newSettings['site_favicon'] = $uploadResult['relative_path'];
        } else {
            setAlert('Error uploading favicon: ' . $uploadResult['message'], 'danger');
        }
    }

    // حفظ الإعدادات
    if (saveSiteSettings($newSettings)) {
        setAlert('Settings updated successfully', 'success');
        // تحديث الإعدادات المحلية
        $settings = getSiteSettings();
    } else {
        setAlert('Error saving settings', 'danger');
    }
}

// تضمين ملف الرأس
include 'header.php';
?>

<div class="settings-container">
    <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">
                <i class="fas fa-cog me-2"></i>General
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="appearance-tab" data-bs-toggle="tab" data-bs-target="#appearance" type="button" role="tab" aria-controls="appearance" aria-selected="false">
                <i class="fas fa-palette me-2"></i>Appearance
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="account-tab" data-bs-toggle="tab" data-bs-target="#account" type="button" role="tab" aria-controls="account" aria-selected="false">
                <i class="fas fa-user-shield me-2"></i>Account
            </button>
        </li>
    </ul>

    <form method="post" action="" enctype="multipart/form-data">
        <div class="tab-content" id="settingsTabContent">
            <!-- General Settings Tab -->
            <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-globe me-2"></i>Site Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="site_name">
                                        <i class="fas fa-signature me-2"></i>Site Name
                                    </label>
                                    <input type="text" id="site_name" name="site_name" class="form-control" value="<?php echo $settings['site_name']; ?>" required>
                                    <small class="form-text text-muted">This name will appear in the browser title and throughout the site</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="admin_email">
                                        <i class="fas fa-envelope me-2"></i>Admin Email
                                    </label>
                                    <input type="email" id="admin_email" name="admin_email" class="form-control" value="<?php echo $settings['admin_email']; ?>">
                                    <small class="form-text text-muted">Used for system notifications</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Appearance Tab -->
            <div class="tab-pane fade" id="appearance" role="tabpanel" aria-labelledby="appearance-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-image me-2"></i>Site Branding</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="site_logo">
                                        <i class="fas fa-image me-2"></i>Site Logo
                                    </label>
                                    <?php if (isset($settings['site_logo']) && !empty($settings['site_logo'])): ?>
                                    <div class="mb-3 mt-2 image-preview">
                                        <img src="<?php echo $settings['site_logo']; ?>" alt="Site Logo" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">
                                    </div>
                                    <?php endif; ?>
                                    <div class="custom-file-container">
                                        <input type="file" id="site_logo" name="site_logo" class="form-control" accept="image/*">
                                        <small class="form-text text-muted">Recommended size: 200x200 pixels. Leave empty to keep current logo.</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="site_favicon">
                                        <i class="fas fa-bookmark me-2"></i>Site Favicon
                                    </label>
                                    <?php if (isset($settings['site_favicon']) && !empty($settings['site_favicon'])): ?>
                                    <div class="mb-3 mt-2 image-preview">
                                        <img src="<?php echo $settings['site_favicon']; ?>" alt="Site Favicon" class="img-thumbnail" style="max-width: 64px; max-height: 64px;">
                                    </div>
                                    <?php endif; ?>
                                    <div class="custom-file-container">
                                        <input type="file" id="site_favicon" name="site_favicon" class="form-control" accept="image/*">
                                        <small class="form-text text-muted">Recommended size: 32x32 pixels. Leave empty to keep current favicon.</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Tab -->
            <div class="tab-pane fade" id="account" role="tabpanel" aria-labelledby="account-tab">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-user-cog me-2"></i>Admin Account</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="admin_username">
                                        <i class="fas fa-user me-2"></i>Admin Username
                                    </label>
                                    <input type="text" id="admin_username" name="admin_username" class="form-control" value="<?php echo $settings['admin_username']; ?>">
                                    <small class="form-text text-muted">Change your admin username</small>
                                </div>
                            </div>
                        </div>

                        <div class="password-section mt-4">
                            <h5><i class="fas fa-key me-2"></i>Change Password</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="current_password">Current Password</label>
                                        <div class="password-container position-relative">
                                            <input type="password" id="current_password" name="current_password" class="form-control">
                                            <span class="password-toggle position-absolute end-0 top-50 translate-middle-y pe-3" style="cursor: pointer;">
                                                <i class="fas fa-eye toggle-password" data-target="current_password"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="new_password">New Password</label>
                                        <div class="password-container position-relative">
                                            <input type="password" id="new_password" name="new_password" class="form-control">
                                            <span class="password-toggle position-absolute end-0 top-50 translate-middle-y pe-3" style="cursor: pointer;">
                                                <i class="fas fa-eye toggle-password" data-target="new_password"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="confirm_password">Confirm New Password</label>
                                        <div class="password-container position-relative">
                                            <input type="password" id="confirm_password" name="confirm_password" class="form-control">
                                            <span class="password-toggle position-absolute end-0 top-50 translate-middle-y pe-3" style="cursor: pointer;">
                                                <i class="fas fa-eye toggle-password" data-target="confirm_password"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <small class="form-text text-muted">Leave password fields empty if you don't want to change the password</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-actions mt-4 d-flex justify-content-between">
            <div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>Save Settings
                </button>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-times me-2"></i>Cancel
                </a>
            </div>
            <div class="d-none d-md-block">
                <span class="text-muted">Last updated: <?php echo date('F j, Y, g:i a', filemtime('settings.json')); ?></span>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const toggleButtons = document.querySelectorAll('.toggle-password');
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const passwordInput = document.getElementById(targetId);

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                this.classList.remove('fa-eye');
                this.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                this.classList.remove('fa-eye-slash');
                this.classList.add('fa-eye');
            }
        });
    });

    // Remember active tab
    const hash = window.location.hash;
    if (hash) {
        const tab = document.querySelector(`[data-bs-target="${hash}"]`);
        if (tab) {
            const tabInstance = new bootstrap.Tab(tab);
            tabInstance.show();
        }
    }

    // Store active tab in URL
    const tabEls = document.querySelectorAll('button[data-bs-toggle="tab"]');
    tabEls.forEach(tabEl => {
        tabEl.addEventListener('shown.bs.tab', function (event) {
            const target = event.target.getAttribute('data-bs-target');
            window.location.hash = target;
        });
    });

    // Preview uploaded images
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const preview = this.closest('.form-group').querySelector('img');
            if (preview && this.files && this.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.closest('.image-preview').style.display = 'block';
                }
                reader.readAsDataURL(this.files[0]);
            }
        });
    });
});
</script>

<?php
// تضمين ملف التذييل
include 'footer.php';
?>
