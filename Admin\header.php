<?php
// منع الوصول المباشر للملف
if (!defined('ADMIN_ACCESS')) {
    header('Location: ../index.php');
    exit;
}

// الحصول على إعدادات الموقع
$settings = getSiteSettings();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle ?? 'Admin Panel'; ?> - <?php echo $settings['site_name']; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="style.css">
    <?php if (isset($settings['site_favicon']) && !empty($settings['site_favicon'])): ?>
    <link rel="icon" href="<?php echo $settings['site_favicon']; ?>" type="image/x-icon">
    <?php endif; ?>
</head>
<body>
    <?php if (isLoggedIn()): ?>
    <div class="admin-container">
        <div class="admin-sidebar" id="adminSidebar">
            <div class="sidebar-header">
                <img src="<?php echo $settings['site_logo']; ?>" alt="<?php echo $settings['site_name']; ?> Logo" class="sidebar-logo">
                <h3><?php echo $settings['site_name']; ?></h3>
                <button class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <ul class="sidebar-menu">
                <li><a href="index.php" <?php echo basename($_SERVER['PHP_SELF']) === 'index.php' ? 'class="active"' : ''; ?>><i class="fas fa-tachometer-alt"></i> <span>Dashboard</span></a></li>
                <li><a href="games.php" <?php echo basename($_SERVER['PHP_SELF']) === 'games.php' ? 'class="active"' : ''; ?>><i class="fas fa-gamepad"></i> <span>Games</span></a></li>
                <li><a href="settings.php" <?php echo basename($_SERVER['PHP_SELF']) === 'settings.php' ? 'class="active"' : ''; ?>><i class="fas fa-cog"></i> <span>Settings</span></a></li>
                <li><a href="index.php?logout=1"><i class="fas fa-sign-out-alt"></i> <span>Logout</span></a></li>
            </ul>
        </div>
        <div class="admin-content">
            <div class="content-header">
                <h2><?php echo $pageTitle ?? 'Dashboard'; ?></h2>

            </div>
            <div class="content-body">
                <?php echo displayAlert(); ?>
    <?php endif; ?>
