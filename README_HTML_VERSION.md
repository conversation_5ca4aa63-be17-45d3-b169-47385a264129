# Filora - HTML Version

## نظرة عامة
تم تحويل المشروع من PHP إلى HTML/CSS/JavaScript خالص بدون لوحة تحكم. النسخة الجديدة تحتفظ بنفس التصميم والوظائف مع تبسيط الهيكل.

## الملفات الرئيسية

### ملفات HTML
- `index.html` - الصفحة الرئيسية
- `game.html` - صفحة اللعبة

### ملفات التكوين (JSON)
- `settings.json` - إعدادات الموقع
- `games.json` - بيانات الألعاب

### ملفات JavaScript
- `script.js` - سكريبت الصفحة الرئيسية
- `game.js` - سكريبت صفحة اللعبة

### ملفات CSS
- `styles.css` - تصميم الصفحة الرئيسية
- `game.css` - تصميم صفحة اللعبة

### مجلد الصور
- `img/` - يحتوي على جميع الصور والأيقونات

### الملفات القديمة
- `old/` - يحتوي على النسخة PHP القديمة

## الميزات

### ✅ الميزات المحتفظ بها:
- نفس التصميم والألوان
- اختيار كميات العملة المخصصة
- تأثيرات التحميل والمعالجة
- البحث في الألعاب
- تصميم متجاوب

### 🆕 التحسينات الجديدة:
- لا حاجة لخادم PHP
- ملفات JSON بسيطة للتكوين
- سهولة التخصيص والتعديل
- أداء أفضل
- استضافة أسهل

## كيفية الاستخدام

### تشغيل المشروع:
1. افتح `index.html` في المتصفح مباشرة
2. أو استخدم خادم محلي بسيط:
   ```bash
   python -m http.server 8000
   # أو
   npx serve .
   ```

### تخصيص الإعدادات:
عدّل ملف `settings.json`:
```json
{
    "site_name": "اسم موقعك",
    "site_logo": "مسار الشعار",
    "tagline": "الشعار النصي",
    "main_title": "العنوان الرئيسي"
}
```

### إضافة/تعديل الألعاب:
عدّل ملف `games.json`:
```json
{
    "games": [
        {
            "id": "game_id",
            "name": "اسم اللعبة",
            "currency": "اسم العملة",
            "currencyAmounts": [500, 1000, 1500],
            "image": "مسار صورة اللعبة",
            "currencyIcon": "مسار أيقونة العملة",
            "backgroundColor": "#لون_الخلفية",
            "available": true
        }
    ]
}
```

## هيكل المجلدات
```
├── index.html          # الصفحة الرئيسية
├── game.html           # صفحة اللعبة
├── settings.json       # إعدادات الموقع
├── games.json          # بيانات الألعاب
├── script.js           # سكريبت الصفحة الرئيسية
├── game.js             # سكريبت صفحة اللعبة
├── styles.css          # تصميم الصفحة الرئيسية
├── game.css            # تصميم صفحة اللعبة
├── img/                # مجلد الصور
│   ├── currency/       # أيقونات العملات
│   └── ...
└── old/                # النسخة PHP القديمة
    ├── Admin/
    ├── *.php
    └── ...
```

## المتطلبات
- متصفح ويب حديث
- لا حاجة لخادم PHP
- (اختياري) خادم ويب بسيط للتطوير

## الاستضافة
يمكن استضافة المشروع على أي خدمة استضافة ثابتة مثل:
- GitHub Pages
- Netlify
- Vercel
- Firebase Hosting
- أي خادم ويب عادي

## ملاحظات
- تم نقل جميع ملفات PHP إلى مجلد `old/`
- الصور المرفوعة سابقاً متاحة في `old/Admin/Uploads/`
- يمكن حذف مجلد `old/` إذا لم تعد بحاجة للنسخة القديمة
