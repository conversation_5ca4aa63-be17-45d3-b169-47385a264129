<?php
// تضمين ملف الإعدادات
require_once 'Admin/config.php';

// الحصول على إعدادات الموقع
$settings = getSiteSettings();

// تعيين متغيرات الصفحة
$siteName = $settings['site_name'] ?? 'Mppl.pro';
$siteLogo = $settings['site_logo'] ?? 'img/rocket.svg';
$siteFavicon = $settings['site_favicon'] ?? 'img/favicon.ico';
$siteDescription = $settings['site_description'] ?? 'Get free game currency and resources';
$siteKeywords = $settings['site_keywords'] ?? 'games, free currency, resources';
$themeColor = $settings['theme_color'] ?? '#4a00e0';

// التحقق من وضع الصيانة
$maintenanceMode = $settings['maintenance_mode'] ?? false;
$maintenanceMessage = $settings['maintenance_message'] ?? 'Site is under maintenance. Please check back later.';

// إذا كان الموقع في وضع الصيانة وليس المستخدم مسجل الدخول كمسؤول
if ($maintenanceMode && !isset($_SESSION['admin_logged_in'])) {
    // عرض صفحة الصيانة
    include 'maintenance.php';
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle ?? $siteName; ?></title>
    <meta name="description" content="<?php echo $siteDescription; ?>">
    <meta name="keywords" content="<?php echo $siteKeywords; ?>">
    <meta name="theme-color" content="<?php echo $themeColor; ?>">
    <link rel="icon" href="<?php echo $siteFavicon; ?>" type="image/x-icon">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">

    <?php if (isset($settings['enable_analytics']) && $settings['enable_analytics'] && !empty($settings['analytics_code'])): ?>
    <!-- Analytics Code -->
    <?php echo $settings['analytics_code']; ?>
    <?php endif; ?>
</head>
<body>
