<?php
// منع الوصول المباشر للملف
if (!defined('ADMIN_ACCESS')) {
    header('Location: ../index.php');
    exit;
}
?>
<?php if (isLoggedIn()): ?>
            </div>
            <div class="content-footer">
                <p>&copy; <?php echo date('Y'); ?> <?php echo $settings['site_name']; ?> - Admin Panel</p>
            </div>
        </div>
    </div>
<?php endif; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Mobile menu toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const adminSidebar = document.getElementById('adminSidebar');

            if (mobileMenuToggle && adminSidebar) {
                mobileMenuToggle.addEventListener('click', function() {
                    adminSidebar.classList.toggle('collapsed');
                });
            }

            // Handle responsive tables
            const tables = document.querySelectorAll('table');
            tables.forEach(table => {
                if (!table.classList.contains('table-responsive') && !table.parentElement.classList.contains('table-responsive')) {
                    const wrapper = document.createElement('div');
                    wrapper.classList.add('table-responsive');
                    table.parentNode.insertBefore(wrapper, table);
                    wrapper.appendChild(table);
                }
            });

            // Improve form layout on mobile
            const formGroups = document.querySelectorAll('.form-group');
            formGroups.forEach(group => {
                const label = group.querySelector('label');
                const input = group.querySelector('input, select, textarea');
                if (label && input) {
                    label.setAttribute('for', input.id || '');
                }
            });
        });
    </script>
    <?php if (isset($extraScripts)) echo $extraScripts; ?>
</body>
</html>
