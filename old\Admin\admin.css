/* Admin Dashboard Styles */
.admin-body {
    background-color: #f5f5f5;
}

.admin-container {
    display: flex;
    min-height: 100vh;
}

.admin-sidebar {
    width: 250px;
    background: linear-gradient(135deg, #4a00e0, #8e2de2);
    color: white;
    padding: 20px 0;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.admin-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 20px;
}

#admin-logo-img {
    width: 50px;
    height: auto;
    margin-bottom: 10px;
}

.admin-menu {
    list-style: none;
}

.admin-menu li {
    margin-bottom: 5px;
}

.admin-menu a {
    display: block;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s;
}

.admin-menu a:hover, .admin-menu li.active a {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.admin-menu i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.admin-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
}

.admin-btn {
    background: linear-gradient(90deg, #4a00e0, #8e2de2);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s;
}

.admin-btn:hover {
    box-shadow: 0 5px 15px rgba(74, 0, 224, 0.3);
    transform: translateY(-2px);
}

/* Game Table Styles */
.games-table-container {
    margin-top: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.games-table {
    width: 100%;
    border-collapse: collapse;
}

.games-table th, .games-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.games-table th {
    background-color: #f9f9f9;
    font-weight: 600;
}

.game-image-cell img {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    object-fit: cover;
}

.game-actions {
    display: flex;
    gap: 10px;
}

.edit-btn, .delete-btn {
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    border: none;
}

.edit-btn {
    background-color: #3498db;
    color: white;
}

.delete-btn {
    background-color: #e74c3c;
    color: white;
}

/* Modal Styles */
.admin-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 30px;
    width: 500px;
    max-width: 90%;
    border-radius: 10px;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.2);
    position: relative;
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 24px;
    cursor: pointer;
    color: #aaa;
}

.close-modal:hover {
    color: #333;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input, .form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.logo-preview {
    width: 100px;
    height: 100px;
    border: 1px dashed #ddd;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    overflow: hidden;
}

#logo-preview-img {
    max-width: 100%;
    max-height: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-container {
        flex-direction: column;
    }
    
    .admin-sidebar {
        width: 100%;
        padding: 10px 0;
    }
    
    .admin-logo {
        flex-direction: row;
        justify-content: center;
        padding: 10px;
    }
    
    #admin-logo-img {
        margin-right: 10px;
        margin-bottom: 0;
    }
    
    .admin-menu {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .admin-menu li {
        margin: 0 5px;
    }
    
    .admin-menu a {
        padding: 8px 15px;
    }
}