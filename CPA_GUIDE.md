# دليل نظام CPA الديناميكي

## ✅ تم تطبيق النظام بنجاح!

### 🎯 المفهوم الأساسي:
- كل لعبة لها CPA script خاص بها في `games.json`
- يتم تحميل السكريبت ديناميكياً عند فتح اللعبة
- التفعيل يحدث عند النقر على أزرار العملات بعد 13 ثانية

## إعداد CPA للألعاب

### ✅ لعبة مع CPA:
```json
{
    "id": "roblox",
    "cpa_script": "<script type=\"text/javascript\">var tIjwE_WXF_CxIRMc={\"it\":4469056,\"key\":\"e990e\"};</script><script src=\"https://dfmpe7igjx4jo.cloudfront.net/aeb5ad1.js\"></script>",
    "cpa_function": "_qW"
}
```

### ❌ لعبة بدون CPA:
```json
{
    "id": "fortnite",
    "cpa_script": "",
    "cpa_function": ""
}
```

## الألعاب المُعدة حالياً

### مع CPA:
- **Roblox**: `tIjwE_WXF_CxIRMc={"it":4469056,"key":"e990e"}` + دالة `_qW`
- **PUBG Mobile**: `tIjwE_WXF_CxIRMc={"it":4469056,"key":"e990e"}` + دالة `_qW`
- **Free Fire**: `tIjwE_WXF_CxIRMc={"it":9876543,"key":"ff123"}` + دالة `_VX`

### بدون CPA:
- **Fortnite**: رسالة نجاح عادية
- **Minecraft**: رسالة نجاح عادية

## كيفية الاختبار

### 🧪 للتحقق من تحميل CPA:
1. افتح Developer Tools (F12)
2. اذهب إلى Console
3. افتح أي لعبة مع CPA
4. ابحث عن رسائل مثل:
   ```
   🔄 Loading CPA Script for game: Roblox
   📜 Script content: <script type="text/javascript">...
   ✅ CPA script added to container
   🎉 CPA scripts loaded successfully for Roblox
   🎯 CPA Function: _qW
   ```

### 🎮 لاختبار التفعيل:
1. انقر على أي زر عملة
2. ابحث عن: `Currency option clicked, CPA will activate in 13 seconds...`
3. أكمل العملية حتى 88%
4. انتظر 13 ثانية لرؤية: `Attempting to activate CPA function: _qW`

## إضافة لعبة جديدة مع CPA

### خطوات الإعداد:
1. افتح `games.json`
2. أضف اللعبة الجديدة:
```json
{
    "id": "new_game",
    "name": "New Game",
    "currency": "Coins",
    "cpa_script": "<script type=\"text/javascript\">var tIjwE_WXF_CxIRMc={\"it\":YOUR_ID,\"key\":\"YOUR_KEY\"};</script><script src=\"YOUR_CPA_URL\"></script>",
    "cpa_function": "YOUR_FUNCTION_NAME"
}
```

## المزايا

### ✅ مرونة كاملة:
- كل لعبة لها CPA script منفصل
- يمكن استخدام دوال CPA مختلفة
- سهولة إضافة/إزالة CPA لأي لعبة

### ✅ أداء محسن:
- تحميل السكريبت فقط عند الحاجة
- مسح السكريبت السابق عند تغيير اللعبة
- لا توجد تداخلات بين scripts مختلفة

### ✅ سهولة الصيانة:
- جميع إعدادات CPA في مكان واحد
- تسجيل مفصل للتشخيص
- دعم تلقائي لدوال CPA متعددة

## روابط الاختبار
- Roblox (مع CPA): http://localhost:8000/game.html?id=roblox
- PUBG (مع CPA): http://localhost:8000/game.html?id=pubg
- Free Fire (مع CPA): http://localhost:8000/game.html?id=freefire
- Fortnite (بدون CPA): http://localhost:8000/game.html?id=fortnite
