/* Admin Panel Styles */
:root {
    --primary-color: #4a00e0;
    --secondary-color: #8e2de2;
    --dark-color: #333;
    --light-color: #f4f4f4;
    --danger-color: #dc3545;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
}

/* Login Page */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 20px;
}

.login-box {
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    padding: 35px;
    width: 420px;
    max-width: 100%;
    transition: all 0.3s ease;
}

.login-logo {
    text-align: center;
    margin-bottom: 25px;
}

.login-logo img {
    width: 90px;
    height: 90px;
    object-fit: contain;
    border-radius: 15px;
    padding: 5px;
    background-color: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;
}

.login-logo img:hover {
    transform: scale(1.05);
}

.login-logo h2 {
    margin: 15px 0 5px;
    color: var(--dark-color);
    font-weight: 600;
    font-size: 24px;
}

.login-form .form-group {
    margin-bottom: 22px;
}

.login-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-color);
    font-size: 15px;
}

.login-form input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background-color: #f9f9f9;
}

.login-form input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 0, 224, 0.1);
    background-color: white;
    outline: none;
}

.btn-login {
    width: 100%;
    padding: 14px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
}

.btn-login:hover {
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.back-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-block;
    padding: 5px 10px;
    border-radius: 5px;
}

.back-link:hover {
    color: var(--secondary-color);
    background-color: rgba(74, 0, 224, 0.05);
}

.password-container {
    position: relative;
}

.password-toggle {
    color: #777;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: var(--primary-color);
}

/* Admin Layout */
.admin-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

.admin-sidebar {
    width: 250px;
    background: linear-gradient(180deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    transition: all 0.3s ease;
    z-index: 1000;
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo {
    width: 60px;
    height: 60px;
    margin-bottom: 10px;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    margin: 0;
    padding: 0;
}

.sidebar-menu a {
    display: block;
    padding: 15px 20px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.sidebar-menu a:hover, .sidebar-menu a.active {
    background-color: rgba(255, 255, 255, 0.1);
    border-left-color: white;
}

.sidebar-menu i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.admin-content {
    flex: 1;
    margin-left: 250px;
    padding: 20px;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.content-header h2 {
    margin: 0;
    color: var(--dark-color);
}

.user-info {
    color: var(--dark-color);
    font-weight: 500;
}

.content-body {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 20px;
}

.content-footer {
    text-align: center;
    padding: 10px;
    color: #777;
    font-size: 14px;
}

/* Dashboard */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    padding: 20px;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-card:nth-child(1) {
    border-left-color: var(--primary-color);
}

.stat-card:nth-child(2) {
    border-left-color: var(--success-color);
}



.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
    font-size: 24px;
    transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1);
}

.stat-icon.games {
    background-color: rgba(74, 0, 224, 0.1);
    color: var(--primary-color);
}

.stat-icon.users {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}



.stat-info {
    flex: 1;
}

.stat-info h3 {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
    color: var(--dark-color);
}

.stat-info p {
    margin: 5px 0 0;
    color: #777;
    font-size: 15px;
}

.stat-info small {
    font-size: 12px;
    opacity: 0.7;
}

/* Progress bar */
.progress {
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: 10px;
}

/* List group */
.list-group {
    border-radius: 8px;
    overflow: hidden;
}

.list-group-item {
    padding: 12px 15px;
    border-color: #f0f0f0;
    transition: all 0.2s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.list-group-flush .list-group-item {
    border-right: 0;
    border-left: 0;
    border-radius: 0;
}

/* Games Management */
.games-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.games-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.games-table th, .games-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
}

.games-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    font-size: 13px;
    letter-spacing: 0.5px;
}

.games-table tr:hover, .games-table tr.row-hover {
    background-color: rgba(74, 0, 224, 0.03);
}

.game-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.game-image:hover {
    transform: scale(1.1);
}

.game-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.btn-edit, .btn-delete, .btn-view {
    padding: 5px 10px;
    border-radius: 3px;
    text-decoration: none;
    color: white;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-view {
    background-color: var(--info-color);
}

.btn-edit {
    background-color: var(--warning-color);
}

.btn-delete {
    background-color: var(--danger-color);
}

.btn-view:hover, .btn-edit:hover, .btn-delete:hover {
    opacity: 0.9;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Games filter and search */
.games-filter {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

.badge {
    padding: 5px 10px;
    border-radius: 30px;
    font-weight: 500;
    font-size: 12px;
}

.bg-success {
    background-color: #28a745 !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.bg-info {
    background-color: #17a2b8 !important;
}

.bg-warning {
    background-color: #ffc107 !important;
}

.btn-group {
    display: inline-flex;
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.btn-group .btn:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover, .btn-outline-primary.active {
    background-color: var(--primary-color);
    color: white;
}

.btn-outline-success {
    color: #28a745;
    border-color: #28a745;
}

.btn-outline-success:hover, .btn-outline-success.active {
    background-color: #28a745;
    color: white;
}

.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545;
}

.btn-outline-danger:hover, .btn-outline-danger.active {
    background-color: #dc3545;
    color: white;
}

.small {
    font-size: 85%;
}

.text-muted {
    color: #6c757d !important;
}

/* Forms */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-color);
    font-size: 15px;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 0, 224, 0.1);
    outline: none;
}

.form-check {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.form-check-input {
    margin-right: 8px;
}

.form-text {
    font-size: 13px;
    color: #6c757d;
    margin-top: 5px;
    display: block;
}

.btn {
    padding: 10px 16px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #5a6268;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Custom file input */
.custom-file-container {
    position: relative;
}

.custom-file-container input[type="file"] {
    padding: 8px;
    background-color: #f8f9fa;
}

.image-preview {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
    display: inline-block;
}

.img-thumbnail {
    border: 1px solid #dee2e6;
    padding: 5px;
    border-radius: 6px;
    background-color: white;
    transition: all 0.3s ease;
}

.img-thumbnail:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Tabs styling */
.nav-tabs {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 20px;
}

.nav-tabs .nav-item {
    margin-bottom: -1px;
}

.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    padding: 10px 16px;
    font-weight: 500;
    color: #495057;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    color: var(--primary-color);
}

.nav-tabs .nav-link.active {
    color: var(--primary-color);
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    font-weight: 600;
}

.tab-content > .tab-pane {
    padding: 20px 0;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    padding: 15px 20px;
}

.card-header h5 {
    margin: 0;
    font-weight: 600;
    color: var(--dark-color);
}

.card-body {
    padding: 20px;
}

.form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

/* Alerts */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 5px;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.2);
    color: var(--success-color);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.2);
    color: var(--danger-color);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.2);
    color: var(--warning-color);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border: 1px solid rgba(23, 162, 184, 0.2);
    color: var(--info-color);
}

/* Responsive */
@media (max-width: 992px) {
    .admin-sidebar {
        width: 200px;
    }

    .admin-content {
        margin-left: 200px;
    }

    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .games-table {
        font-size: 14px;
    }

    .game-actions {
        flex-direction: column;
        gap: 5px;
    }

    .btn-edit, .btn-delete, .btn-view {
        text-align: center;
    }
}

@media (max-width: 768px) {
    body {
        font-size: 14px;
    }

    .admin-container {
        flex-direction: column;
    }

    .admin-sidebar {
        width: 100%;
        height: auto;
        position: relative;
        overflow: hidden;
    }

    .sidebar-header {
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .sidebar-logo {
        width: 40px;
        height: 40px;
        margin-bottom: 0;
    }

    .sidebar-header h3 {
        margin: 0 0 0 10px;
        font-size: 18px;
    }

    .sidebar-menu {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }

    .sidebar-menu li {
        width: 50%;
    }

    .sidebar-menu a {
        padding: 10px;
        text-align: center;
        border-left: none;
        border-bottom: 3px solid transparent;
    }

    .sidebar-menu a:hover, .sidebar-menu a.active {
        border-left-color: transparent;
        border-bottom-color: white;
    }

    .sidebar-menu i {
        margin-right: 5px;
    }

    .admin-content {
        margin-left: 0;
        padding: 15px;
    }

    .content-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .games-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .games-table th, .games-table td {
        padding: 8px;
    }

    /* Responsive table for mobile */
    .games-table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }

    /* Form improvements for mobile */
    .form-group {
        margin-bottom: 15px;
    }

    .form-control {
        padding: 8px;
        font-size: 14px;
    }

    /* Card improvements */
    .card {
        margin-bottom: 15px;
    }

    .card-header {
        padding: 10px 15px;
    }

    .card-body {
        padding: 15px;
    }
}

/* Mobile menu toggle */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
}

@media (max-width: 576px) {
    .mobile-menu-toggle {
        display: block;
    }

    .admin-sidebar.collapsed .sidebar-menu {
        display: none;
    }

    .sidebar-header {
        justify-content: space-between;
    }

    .sidebar-menu li {
        width: 100%;
    }

    .login-box {
        width: 90%;
        padding: 20px;
    }

    .stat-card {
        padding: 15px;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .stat-info h3 {
        font-size: 20px;
    }

    /* Improve form layout on small screens */
    .row {
        margin-left: -5px;
        margin-right: -5px;
    }

    .col-md-6, .col-md-12 {
        padding-left: 5px;
        padding-right: 5px;
    }
}
