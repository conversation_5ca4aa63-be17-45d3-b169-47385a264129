<?php
// تعيين رأس الاستجابة إلى JSON
header('Content-Type: application/json');

// تهيئة متغيرات الاستجابة
$response = [
    'success' => false,
    'data' => null,
    'message' => 'Invalid request'
];

// التحقق من نوع الطلب
$action = isset($_GET['action']) ? $_GET['action'] : '';

// معالجة الطلبات المختلفة
switch ($action) {
    case 'get_games':
        // قراءة ملف games.json
        $gamesFile = file_get_contents('games.json');
        if ($gamesFile) {
            $response['success'] = true;
            $response['data'] = json_decode($gamesFile, true);
            $response['message'] = 'Games data retrieved successfully';
        } else {
            $response['message'] = 'Failed to read games data';
        }
        break;
        
    case 'get_game':
        // التحقق من وجود معرف اللعبة
        $gameId = isset($_GET['id']) ? $_GET['id'] : '';
        if (empty($gameId)) {
            $response['message'] = 'Game ID is required';
            break;
        }
        
        // قراءة ملف games.json
        $gamesFile = file_get_contents('games.json');
        if ($gamesFile) {
            $gamesData = json_decode($gamesFile, true);
            
            // البحث عن اللعبة بواسطة المعرف
            $gameFound = false;
            foreach ($gamesData['games'] as $game) {
                if ($game['id'] === $gameId) {
                    $response['success'] = true;
                    $response['data'] = $game;
                    $response['message'] = 'Game data retrieved successfully';
                    $gameFound = true;
                    break;
                }
            }
            
            if (!$gameFound) {
                $response['message'] = 'Game not found';
            }
        } else {
            $response['message'] = 'Failed to read games data';
        }
        break;
        
    case 'get_stats':
        // توليد إحصائيات عشوائية
        $response['success'] = true;
        $response['data'] = [
            'online_users' => rand(500, 1500),
            'last_update' => date('Y-m-d H:i:s'),
            'server_status' => 'online'
        ];
        $response['message'] = 'Stats retrieved successfully';
        break;
        
    default:
        $response['message'] = 'Unknown action';
        break;
}

// إرسال الاستجابة
echo json_encode($response);
?>
