<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="page-title">Game Currency Generator - Filora</title>
    <meta name="description" content="Get free game currency and resources" id="page-description">
    <meta name="keywords" content="games, free currency, resources" id="page-keywords">
    <meta name="theme-color" content="#4a00e0" id="theme-color">
    <link rel="icon" href="img/rocket.svg" type="image/x-icon" id="favicon">
    
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="game.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- CPA Scripts will be injected here dynamically -->
    <div id="cpa-scripts-container"></div>
</head>
<body class="game-page">
    <div class="game-header">
        <div id="game-title">Loading...</div>
    </div>

    <div class="game-container">
        <div class="game-content">
            <div class="game-image-container">
                <img id="game-image" src="img/placeholder-game.svg" alt="Game Image">
            </div>

            <h2 id="game-subtitle">Get Free <span id="currency-name">Currency</span></h2>

            <div class="status-container">
                <div class="status-bar">
                    <div class="status-item">
                        <div class="status-icon"><i class="fas fa-circle status-indicator"></i></div>
                        <div class="status-content">
                            <span class="status-label">STATUS</span>
                            <span class="status-value online">ONLINE</span>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-icon"><i class="fas fa-clock"></i></div>
                        <div class="status-content">
                            <span class="status-label">LAST UPDATE</span>
                            <span class="status-value" id="last-update-value">1 hour ago</span>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-icon"><i class="fas fa-users"></i></div>
                        <div class="status-content">
                            <span class="status-label">ONLINE USERS</span>
                            <span class="status-value" id="online-users-value">1250</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="connection-form">
                <h3>Your <span id="game-name-form">Game</span> Account</h3>
                <p class="input-instruction">Please enter your details accurately to ensure a successful sync with the server.</p>
                <div class="input-container">
                    <img src="img/user-icon.svg" alt="User" class="user-icon">
                    <input type="text" id="username-input" placeholder="Username or ID...">
                </div>
                <button id="connect-btn">Connect</button>
            </div>
        </div>
    </div>

    <div class="loading-overlay" id="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-header">
                <div class="loading-icon">
                    <i class="fas fa-server"></i>
                </div>
                <div class="loading-text" id="loading-title">Connecting to server...</div>
            </div>

            <div class="progress-container">
                <div class="progress-bar" id="progress-bar"></div>
                <div class="progress-percentage" id="progress-percentage">0%</div>
            </div>

            <div class="loading-status-container">
                <div class="status-icon">
                    <i class="fas fa-circle-notch fa-spin"></i>
                </div>
                <div class="status-text">
                    <div class="loading-status" id="loading-status">Searching for account...</div>
                    <div class="loading-details" id="loading-details"></div>
                </div>
            </div>

            <div class="loading-logs">
                <div class="log-title">Connection Log:</div>
                <div class="log-content" id="loading-logs">
                    <div class="log-entry">Initializing secure connection...</div>
                </div>
            </div>
        </div>
    </div>

    <div class="currency-selection-overlay" id="currency-selection-overlay" style="display: none;">
        <div class="currency-selection-content">
            <div class="user-profile">
                <div class="user-avatar">
                    <img src="img/user-avatar.svg" alt="User Avatar">
                </div>
                <div class="username-display">
                    <div class="username-label">USERNAME:</div>
                    <div class="username-value" id="display-username">N</div>
                </div>
            </div>

            <div class="currency-selection-title">
                Amount of <span id="currency-name-selection">UC</span>
            </div>

            <p class="selection-instruction">Select the amount of resources you wish to add. The daily limit is 6000 units.</p>

            <div class="currency-options" id="currency-options">
                <!-- Currency options will be dynamically added here -->
            </div>
        </div>
    </div>

    <div class="processing-overlay" id="processing-overlay" style="display: none;">
        <div class="processing-content">
            <div class="processing-icon" id="processing-icon"><i class="fas fa-spinner fa-spin"></i></div>
            <div class="processing-status" id="processing-status">Loading...</div>
            <div class="progress-container">
                <div class="progress-bar" id="processing-progress-bar"></div>
                <div class="progress-percentage" id="processing-percentage">0%</div>
            </div>
            <div class="step-indicator">
                <div class="step active" data-step="1"></div>
                <div class="step" data-step="2"></div>
                <div class="step" data-step="3"></div>
                <div class="step" data-step="4"></div>
                <div class="step" data-step="5"></div>
                <div class="step" data-step="6"></div>
            </div>
            <div class="manual-verification" id="manual-verification" style="display: none;"></div>
        </div>
    </div>

    <script src="game.js"></script>
</body>
</html>
