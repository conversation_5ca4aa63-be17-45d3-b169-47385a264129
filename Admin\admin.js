document.addEventListener('DOMContentLoaded', function() {
    // تبديل بين أقسام لوحة التحكم
    const menuItems = document.querySelectorAll('.admin-menu li a');
    const sections = document.querySelectorAll('.admin-section');
    
    menuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // إزالة الفئة النشطة من جميع العناصر
            menuItems.forEach(i => i.parentElement.classList.remove('active'));
            sections.forEach(s => s.classList.remove('active'));
            
            // إضافة الفئة النشطة للعنصر المحدد
            this.parentElement.classList.add('active');
            
            // عرض القسم المناسب
            const targetId = this.getAttribute('href').substring(1);
            document.getElementById(targetId).classList.add('active');
        });
    });
    
    // تحميل الألعاب من ملف JSON
    loadGames();
    
    // إعداد نافذة إضافة/تعديل لعبة
    const addGameBtn = document.getElementById('add-game-btn');
    const gameModal = document.getElementById('game-modal');
    const closeModal = document.querySelector('.close-modal');
    
    addGameBtn.addEventListener('click', function() {
        openGameModal();
    });
    
    closeModal.addEventListener('click', function() {
        gameModal.style.display = 'none';
    });
    
    // إغلاق النافذة عند النقر خارجها
    window.addEventListener('click', function(e) {
        if (e.target === gameModal) {
            gameModal.style.display = 'none';
        }
    });
    
    // تحميل إعدادات الموقع
    loadSiteSettings();
    
    // معالجة نموذج إعدادات الموقع
    const siteSettingsForm = document.getElementById('site-settings-form');
    siteSettingsForm.addEventListener('submit', function(e) {
        e.preventDefault();
        saveSiteSettings();
    });
});

// تحميل الألعاب من ملف JSON
function loadGames() {
    fetch('../games.json')
        .then(response => response.json())
        .then(data => {
            displayGames(data.games);
        })
        .catch(error => {
            console.error('Error loading games:', error);
        });
}

// عرض الألعاب في الجدول
function displayGames(games) {
    const gamesList = document.getElementById('games-list');
    gamesList.innerHTML = '';
    
    games.forEach(game => {
        const row = document.createElement('tr');
        
        // صورة اللعبة
        const imageCell = document.createElement('td');
        imageCell.className = 'game-image-cell';
        const img = document.createElement('img');
        img.src = game.image || '../img/placeholder-game.svg';
        img.alt = game.name;
        img.onerror = function() { this.src = '../img/placeholder-game.svg'; };
        imageCell.appendChild(img);
        
        // اسم اللعبة
        const nameCell = document.createElement('td');
        nameCell.textContent = game.name;
        
        // عملة اللعبة
        const currencyCell = document.createElement('td');
        currencyCell.textContent = game.currency || 'N/A';
        
        // حالة اللعبة
        const statusCell = document.createElement('td');
        const statusBadge = document.createElement('span');
        statusBadge.className = game.available ? 'status-badge active' : 'status-badge inactive';
        statusBadge.textContent = game.available ? 'Active' : 'Inactive';
        statusCell.appendChild(statusBadge);
        
        // أزرار الإجراءات
        const actionsCell = document.createElement('td');
        actionsCell.className = 'game-actions';
        
        const editBtn = document.createElement('button');
        editBtn.className = 'edit-btn';
        editBtn.innerHTML = '<i class="fas fa-edit"></i> Edit';
        editBtn.addEventListener('click', () => editGame(game));
        
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'delete-btn';
        deleteBtn.innerHTML = '<i class="fas fa-trash"></i> Delete';
        deleteBtn.addEventListener('click', () => deleteGame(game.id));
        
        actionsCell.appendChild(editBtn);
        actionsCell.appendChild(deleteBtn);
        
        // إضافة الخلايا إلى الصف
        row.appendChild(imageCell);
        row.appendChild(nameCell);
        row.appendChild(currencyCell);
        row.appendChild(statusCell);
        row.appendChild(actionsCell);
        
        // إضافة الصف إلى الجدول
        gamesList.appendChild(row);
    });
}

// فتح نافذة إضافة لعبة جديدة
function openGameModal(game = null) {
    const gameModal = document.getElementById('game-modal');
    const modalTitle = document.getElementById('modal-title');
    const gameForm = document.getElementById('game-form');
    
    // تعيين عنوان النافذة
    modalTitle.textContent = game ? 'Edit Game' : 'Add New Game';
    
    // إنشاء نموذج اللعبة
    gameForm.innerHTML = `
        <div class="form-group">
            <label for="game-id">Game ID</label>
            <input type="text" id="game-id" ${game ? 'readonly' : ''} value="${game ? game.id : ''}">
        </div>
        <div class="form-group">
            <label for="game-name">Game Name</label>
            <input type="text" id="game-name" value="${game ? game.name : ''}">
        </div>
        <div class="form-group">
            <label for="game-currency">Currency Name</label>
            <input type="text" id="game-currency" value="${game ? game.currency : ''}">
        </div>
        <div class="form-group">
            <label for="game-image">Game Image URL</label>
            <input type="text" id="game-image" value="${game ? game.image : ''}">
        </div>
        <div class="form-group">
            <label for="game-bg-color">Background Color</label>
            <input type="color" id="game-bg-color" value="${game ? game.backgroundColor : '#1e88e5'}">
        </div>
        <div class="form-group">
            <label for="game-status">Status</label>
            <select id="game-status">
                <option value="true" ${game && game.available ? 'selected' : ''}>Active</option>
                <option value="false" ${game && !game.available ? 'selected' : ''}>Inactive</option>
            </select>
        </div>
        <button type="submit" class="admin-btn">${game ? 'Update Game' : 'Add Game'}</button>
    `;
    
    // معالجة تقديم النموذج
    gameForm.addEventListener('submit', function(e) {
        e.preventDefault();
        saveGame(game ? game.id : null);
    });
    
    // عرض النافذة
    gameModal.style.display = 'block';
}

// حفظ بيانات اللعبة
function saveGame(existingId) {
    // جمع بيانات النموذج
    const gameId = document.getElementById('game-id').value;
    const gameName = document.getElementById('game-name').value;
    const gameCurrency = document.getElementById('game-currency').value;
    const gameImage = document.getElementById('game-image').value;
    const gameBgColor = document.getElementById('game-bg-color').value;
    const gameStatus = document
}    