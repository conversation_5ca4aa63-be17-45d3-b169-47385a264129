// سنقوم بتحميل بيانات الألعاب من ملف JSON
let games = [];

// دالة لإنشاء بطاقات الألعاب
function createGameCards(gamesData) {
    const gameCardsContainer = document.getElementById('game-cards-container');
    gameCardsContainer.innerHTML = '';

    gamesData.forEach(game => {
        // تخطي الألعاب غير المتاحة إذا كانت الخاصية موجودة
        if (game.available === false) return;

        const gameCard = document.createElement('div');
        gameCard.className = 'game-card';

        // إضافة لون خلفية مخصص للصورة إذا كان موجوداً
        const bgColor = game.backgroundColor || '#1e88e5';

        // استخدام الصورة الافتراضية إذا لم تكن صورة اللعبة موجودة
        const imageSrc = game.image || 'img/placeholder-game.svg';

        // إضافة معلومات العملة إذا كانت موجودة
        const currencyInfo = game.currency ? `<div class="game-currency">${game.currency}</div>` : '';

        gameCard.innerHTML = `
            <div class="game-img-container" style="background-color: ${bgColor}">
                <img src="${imageSrc}" alt="${game.name}" class="game-img" onerror="this.src='img/placeholder-game.svg'">
            </div>
            <div class="game-name">${game.name}</div>
            ${currencyInfo}
            <button class="start-btn" onclick="startGame('${game.id}')">START</button>
        `;

        gameCardsContainer.appendChild(gameCard);
    });
}

// دالة للتعامل مع البحث
function handleSearch() {
    const searchInput = document.getElementById('search-input');
    const searchTerm = searchInput.value.toLowerCase();

    const filteredGames = games.filter(game => {
        // البحث في اسم اللعبة
        if (game.name.toLowerCase().includes(searchTerm)) return true;

        // البحث في اسم العملة إذا كانت موجودة
        if (game.currency && game.currency.toLowerCase().includes(searchTerm)) return true;

        return false;
    });

    createGameCards(filteredGames);
}

// دالة لبدء اللعبة
function startGame(gameId) {
    // البحث عن اللعبة بواسطة المعرف
    const game = games.find(g => g.id === gameId);

    if (game) {
        // توجيه المستخدم إلى صفحة اللعبة PHP
        window.location.href = `game.php?id=${gameId}`;
    } else {
        alert('اللعبة غير موجودة!');
    }
}

// دالة لتحميل بيانات الألعاب من ملف JSON
async function loadGamesData() {
    try {
        const response = await fetch('games.json');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        games = data.games;
        createGameCards(games);
    } catch (error) {
        console.error('خطأ في تحميل بيانات الألعاب:', error);
    }
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // تحميل بيانات الألعاب من ملف JSON
    loadGamesData();

    // إعداد وظيفة البحث
    const searchInput = document.getElementById('search-input');
    searchInput.addEventListener('input', handleSearch);
});
