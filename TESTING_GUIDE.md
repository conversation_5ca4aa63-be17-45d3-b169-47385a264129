# دليل اختبار النظام المحدث

## اختبار رسالة النجاح (بدون CPA)

### الخطوات:
1. افتح أي لعبة (مثل: `game.html?id=roblox`)
2. أ<PERSON><PERSON>ل اسم مستخدم وهمي
3. اضغط "Connect"
4. انتظر انتهاء التحميل
5. اختر كمية عملة
6. راقب العملية حتى النهاية

### النتيجة المتوقعة:
- ✅ التقدم يصل إلى 100%
- ✅ رسالة: `🎉 SUCCESS! [amount] [currency] has been added to your account!`
- ✅ كونفيتي متساقط ملون
- ✅ تأثير وهج للنص
- ✅ تأثير فلاش أخضر للشاشة
- ✅ صوت نجاح (إذا سُمح بالصوت)

## اختبار رسالة الفشل (مع CPA)

### إعداد CPA للاختبار:
1. افتح `games.json`
2. أضف CPA script للعبة:
```json
{
    "cpa_script": "<script>console.log('CPA loaded');</script>",
    "cpa_function": "_VX"
}
```
3. احفظ الملف

### الخطوات:
1. افتح اللعبة المحدثة
2. أدخل اسم مستخدم وهمي
3. اضغط "Connect"
4. انتظر انتهاء التحميل
5. اختر كمية عملة
6. راقب العملية حتى النهاية

### النتيجة المتوقعة:
- ❌ التقدم يتوقف عند 88%
- ❌ رسالة: `❌ VERIFICATION FAILED - Manual verification required`
- ❌ أيقونة تحذير متحركة
- ❌ تأثير اهتزاز للشاشة
- ❌ ألوان حمراء مع نبض
- ❌ رسالة إرشادية للتحقق اليدوي

## اختبار الألعاب المختلفة

### ألعاب للاختبار:
- `roblox` - Robux (400, 800, 1700, 4500, 10000)
- `pubg` - UC (60, 325, 660, 1800, 3850)
- `freefire` - Diamonds (100, 210, 520, 1080, 2180)
- `fortnite` - V-Bucks (1000, 2800, 5000, 13500, 27000)
- `minecraft` - Minecoins (320, 840, 1720, 3500, 8000)

### تحقق من:
- ✅ تحميل بيانات اللعبة بشكل صحيح
- ✅ عرض كميات العملة المخصصة
- ✅ عمل البحث في الصفحة الرئيسية
- ✅ الألوان المخصصة لكل لعبة
- ✅ الأيقونات والصور

## استكشاف الأخطاء

### إذا لم تظهر التأثيرات:
1. افتح Developer Tools (F12)
2. تحقق من Console للأخطاء
3. تأكد من تحميل ملف game.css
4. تحقق من دعم المتصفح للأنيميشن

### إذا لم تعمل الأصوات:
1. تأكد من السماح للموقع بتشغيل الصوت
2. تحقق من اتصال الإنترنت
3. جرب متصفح مختلف

### إذا لم تظهر الألعاب:
1. تحقق من ملف games.json
2. تأكد من صحة JSON syntax
3. تحقق من مسارات الصور

## نصائح للاختبار

### 🔧 للمطورين:
- استخدم Developer Tools لمراقبة الأخطاء
- اختبر على متصفحات مختلفة
- تحقق من الاستجابة على الأجهزة المحمولة

### 👤 للمستخدمين:
- جرب أسماء مستخدم مختلفة
- اختبر كميات عملة مختلفة
- جرب البحث في الألعاب

### 📱 للأجهزة المحمولة:
- تحقق من التصميم المتجاوب
- اختبر اللمس والتفاعل
- تأكد من وضوح النصوص

## التحقق من الجودة

### ✅ قائمة التحقق:
- [ ] رسالة النجاح تظهر بشكل صحيح
- [ ] رسالة الفشل تظهر عند وجود CPA
- [ ] التأثيرات البصرية تعمل
- [ ] الألوان والأنيميشن سلسة
- [ ] النسب المئوية صحيحة (100% أو 88%)
- [ ] الكونفيتي يتساقط في حالة النجاح
- [ ] الاهتزاز يحدث في حالة الفشل
- [ ] الأصوات تعمل (اختياري)
- [ ] التصميم متجاوب على جميع الأجهزة
