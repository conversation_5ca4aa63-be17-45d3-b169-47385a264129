<?php
// منع الوصول المباشر للملف إلا إذا تم استدعاؤه من ملف آخر
if (!defined('ADMIN_ACCESS') && basename($_SERVER['PHP_SELF']) === 'config.php') {
    header('Location: ../index.php');
    exit;
}

// بدء جلسة إذا لم تكن قد بدأت بالفعل
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// إعدادات قاعدة البيانات (يمكن تعديلها لاحقًا عند إضافة قاعدة بيانات)
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'game_generator');

// إعدادات الموقع
$siteSettings = [
    'site_name' => 'Mppl.pro',
    'site_logo' => '../img/rocket.svg',
    'site_favicon' => '../img/favicon.ico',
    'admin_email' => '<EMAIL>',
    'admin_username' => 'admin',
    'admin_password' => password_hash('admin123', PASSWORD_DEFAULT), // كلمة المرور الافتراضية: admin123
    'games_file' => '../games.json'
];

// دالة للحصول على إعدادات الموقع
function getSiteSettings($forceReload = false) {
    global $siteSettings;
    static $cachedSettings = null;

    // إذا كانت الإعدادات مخزنة مؤقتًا وليس هناك طلب لإعادة التحميل، أعد استخدامها
    if ($cachedSettings !== null && !$forceReload) {
        return $cachedSettings;
    }

    // محاولة قراءة الإعدادات من ملف إذا كان موجودًا
    $settingsFile = __DIR__ . '/settings.json';
    if (file_exists($settingsFile)) {
        $fileContent = file_get_contents($settingsFile);
        if (!empty($fileContent)) {
            $savedSettings = json_decode($fileContent, true);
            if (is_array($savedSettings)) {
                // دمج الإعدادات المحفوظة مع الإعدادات الافتراضية
                $siteSettings = array_merge($siteSettings, $savedSettings);
            }
        }
    } else {
        // إذا لم يكن الملف موجودًا، قم بإنشائه باستخدام الإعدادات الافتراضية
        saveSiteSettings([]);
    }

    // تخزين الإعدادات مؤقتًا للاستخدام اللاحق
    $cachedSettings = $siteSettings;

    return $siteSettings;
}

// دالة للحصول على إعداد محدد
function getSetting($key, $default = null) {
    $settings = getSiteSettings();
    return isset($settings[$key]) ? $settings[$key] : $default;
}

// دالة لحفظ إعدادات الموقع
function saveSiteSettings($settings) {
    global $siteSettings;

    // الحصول على الإعدادات الحالية
    $currentSettings = getSiteSettings();

    // دمج الإعدادات الجديدة مع الإعدادات الحالية
    $updatedSettings = array_merge($currentSettings, $settings);

    // حفظ الإعدادات في ملف
    $settingsFile = __DIR__ . '/settings.json';
    $result = file_put_contents($settingsFile, json_encode($updatedSettings, JSON_PRETTY_PRINT));

    if ($result !== false) {
        // تحديث المتغير العام
        $siteSettings = $updatedSettings;

        // إعادة تعيين الذاكرة المؤقتة في دالة getSiteSettings
        getSiteSettings(true);
    }

    return $result !== false;
}

// دالة لتحديث إعداد محدد
function updateSetting($key, $value) {
    return saveSiteSettings([$key => $value]);
}

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

// دالة للتحقق من صحة بيانات تسجيل الدخول
function validateLogin($username, $password) {
    $settings = getSiteSettings();

    if ($username === $settings['admin_username'] && password_verify($password, $settings['admin_password'])) {
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_username'] = $username;
        return true;
    }

    return false;
}

// دالة لتسجيل الخروج
function logout() {
    unset($_SESSION['admin_logged_in']);
    unset($_SESSION['admin_username']);
    session_destroy();
}
?>
